2025-05-25 03:30:55,881 [INFO] 63809235-383f-4384-a515-ed98d0c921b8 请求: OPTIONS https://api.github.com/copilot_internal/user
2025-05-25 03:30:55,883 [INFO] 63809235-383f-4384-a515-ed98d0c921b8 请求头: {'accept': '*/*', 'access-control-request-method': 'GET', 'access-control-request-headers': 'authorization', 'origin': 'vscode-file://vscode-app', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.2 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'sec-fetch-mode': 'cors', 'sec-fetch-site': 'cross-site', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN', 'priority': 'u=1, i'}
2025-05-25 03:30:55,887 [INFO] 50cea334-22b1-447f-8a47-e043e5aa4f49 请求: OPTIONS https://api.github.com/copilot_internal/user
2025-05-25 03:30:55,889 [INFO] 50cea334-22b1-447f-8a47-e043e5aa4f49 请求头: {'accept': '*/*', 'access-control-request-method': 'GET', 'access-control-request-headers': 'authorization', 'origin': 'vscode-file://vscode-app', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.2 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'sec-fetch-mode': 'cors', 'sec-fetch-site': 'cross-site', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN', 'priority': 'u=1, i'}
2025-05-25 03:30:55,893 [INFO] 600b2811-1c09-425e-9e10-ab74f69848e0 请求: OPTIONS https://api.github.com/copilot_internal/v2/token
2025-05-25 03:30:55,902 [INFO] 600b2811-1c09-425e-9e10-ab74f69848e0 请求头: {'accept': '*/*', 'access-control-request-method': 'GET', 'access-control-request-headers': 'authorization', 'origin': 'vscode-file://vscode-app', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.2 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'sec-fetch-mode': 'cors', 'sec-fetch-site': 'cross-site', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN', 'priority': 'u=1, i'}
2025-05-25 03:30:56,133 [INFO] 63809235-383f-4384-a515-ed98d0c921b8 响应耗时: 0.248s 204 https://api.github.com/copilot_internal/user
2025-05-25 03:30:56,135 [INFO] 63809235-383f-4384-a515-ed98d0c921b8 响应头: {'date': 'Sat, 24 May 2025 19:30:57 GMT', 'access-control-expose-headers': 'ETag, Link, Location, Retry-After, X-GitHub-OTP, X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Used, X-RateLimit-Resource, X-RateLimit-Reset, X-OAuth-Scopes, X-Accepted-OAuth-Scopes, X-Poll-Interval, X-GitHub-Media-Type, X-GitHub-SSO, X-GitHub-Request-Id, Deprecation, Sunset', 'access-control-max-age': '86400', 'access-control-allow-headers': 'Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match, If-Unmodified-Since, Accept-Encoding, X-GitHub-OTP, X-Requested-With, User-Agent, GraphQL-Features, X-Github-Next-Global-ID, X-GitHub-Api-Version, X-Fetch-Nonce', 'access-control-allow-methods': 'GET, POST, PATCH, PUT, DELETE', 'access-control-allow-origin': '*', 'strict-transport-security': 'max-age=31536000; includeSubdomains; preload', 'x-frame-options': 'deny', 'x-content-type-options': 'nosniff', 'x-xss-protection': '0', 'referrer-policy': 'origin-when-cross-origin, strict-origin-when-cross-origin', 'content-security-policy': "default-src 'none'; base-uri 'self'; child-src github.githubassets.com github.com/assets-cdn/worker/ github.com/assets/ gist.github.com/assets-cdn/worker/; connect-src 'self' uploads.github.com www.githubstatus.com collector.github.com raw.githubusercontent.com api.github.com github-cloud.s3.amazonaws.com github-production-repository-file-5c1aeb.s3.amazonaws.com github-production-upload-manifest-file-7fdce7.s3.amazonaws.com github-production-user-asset-6210df.s3.amazonaws.com *.rel.tunnels.api.visualstudio.com wss://*.rel.tunnels.api.visualstudio.com objects-origin.githubusercontent.com copilot-proxy.githubusercontent.com proxy.individual.githubcopilot.com proxy.business.githubcopilot.com proxy.enterprise.githubcopilot.com; font-src github.githubassets.com; form-action 'self' github.com gist.github.com copilot-workspace.githubnext.com objects-origin.githubusercontent.com; frame-ancestors 'none'; frame-src viewscreen.githubusercontent.com notebooks.githubusercontent.com; img-src 'self' data: blob: github.githubassets.com media.githubusercontent.com camo.githubusercontent.com identicons.github.com avatars.githubusercontent.com private-avatars.githubusercontent.com github-cloud.s3.amazonaws.com objects.githubusercontent.com release-assets.githubusercontent.com secured-user-images.githubusercontent.com/ user-images.githubusercontent.com/ private-user-images.githubusercontent.com opengraph.githubassets.com copilotprodattachments.blob.core.windows.net/github-production-copilot-attachments/ github-production-user-asset-6210df.s3.amazonaws.com customer-stories-feed.github.com spotlights-feed.github.com objects-origin.githubusercontent.com; manifest-src 'self'; media-src github.com user-images.githubusercontent.com/ secured-user-images.githubusercontent.com/ private-user-images.githubusercontent.com github-production-user-asset-6210df.s3.amazonaws.com gist.github.com; script-src github.githubassets.com; style-src 'unsafe-inline' github.githubassets.com; upgrade-insecure-requests; worker-src github.githubassets.com github.com/assets-cdn/worker/ github.com/assets/ gist.github.com/assets-cdn/worker/", 'vary': 'Accept-Encoding, Accept, X-Requested-With', 'server': 'github.com', 'x-github-request-id': 'B4F4:3F00B5:1EA8EA8:1FBB75A:68321E71'}
2025-05-25 03:30:56,150 [INFO] 600b2811-1c09-425e-9e10-ab74f69848e0 响应耗时: 0.246s 204 https://api.github.com/copilot_internal/v2/token
2025-05-25 03:30:56,152 [INFO] 600b2811-1c09-425e-9e10-ab74f69848e0 响应头: {'date': 'Sat, 24 May 2025 19:30:57 GMT', 'access-control-expose-headers': 'ETag, Link, Location, Retry-After, X-GitHub-OTP, X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Used, X-RateLimit-Resource, X-RateLimit-Reset, X-OAuth-Scopes, X-Accepted-OAuth-Scopes, X-Poll-Interval, X-GitHub-Media-Type, X-GitHub-SSO, X-GitHub-Request-Id, Deprecation, Sunset', 'access-control-max-age': '86400', 'access-control-allow-headers': 'Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match, If-Unmodified-Since, Accept-Encoding, X-GitHub-OTP, X-Requested-With, User-Agent, GraphQL-Features, X-Github-Next-Global-ID, X-GitHub-Api-Version, X-Fetch-Nonce', 'access-control-allow-methods': 'GET, POST, PATCH, PUT, DELETE', 'access-control-allow-origin': '*', 'strict-transport-security': 'max-age=31536000; includeSubdomains; preload', 'x-frame-options': 'deny', 'x-content-type-options': 'nosniff', 'x-xss-protection': '0', 'referrer-policy': 'origin-when-cross-origin, strict-origin-when-cross-origin', 'content-security-policy': "default-src 'none'; base-uri 'self'; child-src github.githubassets.com github.com/assets-cdn/worker/ github.com/assets/ gist.github.com/assets-cdn/worker/; connect-src 'self' uploads.github.com www.githubstatus.com collector.github.com raw.githubusercontent.com api.github.com github-cloud.s3.amazonaws.com github-production-repository-file-5c1aeb.s3.amazonaws.com github-production-upload-manifest-file-7fdce7.s3.amazonaws.com github-production-user-asset-6210df.s3.amazonaws.com *.rel.tunnels.api.visualstudio.com wss://*.rel.tunnels.api.visualstudio.com objects-origin.githubusercontent.com copilot-proxy.githubusercontent.com proxy.individual.githubcopilot.com proxy.business.githubcopilot.com proxy.enterprise.githubcopilot.com; font-src github.githubassets.com; form-action 'self' github.com gist.github.com copilot-workspace.githubnext.com objects-origin.githubusercontent.com; frame-ancestors 'none'; frame-src viewscreen.githubusercontent.com notebooks.githubusercontent.com; img-src 'self' data: blob: github.githubassets.com media.githubusercontent.com camo.githubusercontent.com identicons.github.com avatars.githubusercontent.com private-avatars.githubusercontent.com github-cloud.s3.amazonaws.com objects.githubusercontent.com release-assets.githubusercontent.com secured-user-images.githubusercontent.com/ user-images.githubusercontent.com/ private-user-images.githubusercontent.com opengraph.githubassets.com copilotprodattachments.blob.core.windows.net/github-production-copilot-attachments/ github-production-user-asset-6210df.s3.amazonaws.com customer-stories-feed.github.com spotlights-feed.github.com objects-origin.githubusercontent.com; manifest-src 'self'; media-src github.com user-images.githubusercontent.com/ secured-user-images.githubusercontent.com/ private-user-images.githubusercontent.com github-production-user-asset-6210df.s3.amazonaws.com gist.github.com; script-src github.githubassets.com; style-src 'unsafe-inline' github.githubassets.com; upgrade-insecure-requests; worker-src github.githubassets.com github.com/assets-cdn/worker/ github.com/assets/ gist.github.com/assets-cdn/worker/", 'vary': 'Accept-Encoding, Accept, X-Requested-With', 'server': 'github.com', 'x-github-request-id': 'B4F4:3F00B5:1EA8EA9:1FBB75E:68321E71'}
2025-05-25 03:30:56,158 [INFO] fe354bd5-c892-40d3-aba0-9611008b3e75 请求: GET https://api.github.com/copilot_internal/user
2025-05-25 03:30:56,163 [INFO] fe354bd5-c892-40d3-aba0-9611008b3e75 请求头: {'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ****************************************', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.2 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'vscode-file://vscode-app', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN', 'priority': 'u=1, i'}
2025-05-25 03:30:56,171 [INFO] 50cea334-22b1-447f-8a47-e043e5aa4f49 响应耗时: 0.280s 204 https://api.github.com/copilot_internal/user
2025-05-25 03:30:56,173 [INFO] 50cea334-22b1-447f-8a47-e043e5aa4f49 响应头: {'date': 'Sat, 24 May 2025 19:30:57 GMT', 'access-control-expose-headers': 'ETag, Link, Location, Retry-After, X-GitHub-OTP, X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Used, X-RateLimit-Resource, X-RateLimit-Reset, X-OAuth-Scopes, X-Accepted-OAuth-Scopes, X-Poll-Interval, X-GitHub-Media-Type, X-GitHub-SSO, X-GitHub-Request-Id, Deprecation, Sunset', 'access-control-max-age': '86400', 'access-control-allow-headers': 'Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match, If-Unmodified-Since, Accept-Encoding, X-GitHub-OTP, X-Requested-With, User-Agent, GraphQL-Features, X-Github-Next-Global-ID, X-GitHub-Api-Version, X-Fetch-Nonce', 'access-control-allow-methods': 'GET, POST, PATCH, PUT, DELETE', 'access-control-allow-origin': '*', 'strict-transport-security': 'max-age=31536000; includeSubdomains; preload', 'x-frame-options': 'deny', 'x-content-type-options': 'nosniff', 'x-xss-protection': '0', 'referrer-policy': 'origin-when-cross-origin, strict-origin-when-cross-origin', 'content-security-policy': "default-src 'none'; base-uri 'self'; child-src github.githubassets.com github.com/assets-cdn/worker/ github.com/assets/ gist.github.com/assets-cdn/worker/; connect-src 'self' uploads.github.com www.githubstatus.com collector.github.com raw.githubusercontent.com api.github.com github-cloud.s3.amazonaws.com github-production-repository-file-5c1aeb.s3.amazonaws.com github-production-upload-manifest-file-7fdce7.s3.amazonaws.com github-production-user-asset-6210df.s3.amazonaws.com *.rel.tunnels.api.visualstudio.com wss://*.rel.tunnels.api.visualstudio.com objects-origin.githubusercontent.com copilot-proxy.githubusercontent.com proxy.individual.githubcopilot.com proxy.business.githubcopilot.com proxy.enterprise.githubcopilot.com; font-src github.githubassets.com; form-action 'self' github.com gist.github.com copilot-workspace.githubnext.com objects-origin.githubusercontent.com; frame-ancestors 'none'; frame-src viewscreen.githubusercontent.com notebooks.githubusercontent.com; img-src 'self' data: blob: github.githubassets.com media.githubusercontent.com camo.githubusercontent.com identicons.github.com avatars.githubusercontent.com private-avatars.githubusercontent.com github-cloud.s3.amazonaws.com objects.githubusercontent.com release-assets.githubusercontent.com secured-user-images.githubusercontent.com/ user-images.githubusercontent.com/ private-user-images.githubusercontent.com opengraph.githubassets.com copilotprodattachments.blob.core.windows.net/github-production-copilot-attachments/ github-production-user-asset-6210df.s3.amazonaws.com customer-stories-feed.github.com spotlights-feed.github.com objects-origin.githubusercontent.com; manifest-src 'self'; media-src github.com user-images.githubusercontent.com/ secured-user-images.githubusercontent.com/ private-user-images.githubusercontent.com github-production-user-asset-6210df.s3.amazonaws.com gist.github.com; script-src github.githubassets.com; style-src 'unsafe-inline' github.githubassets.com; upgrade-insecure-requests; worker-src github.githubassets.com github.com/assets-cdn/worker/ github.com/assets/ gist.github.com/assets-cdn/worker/", 'vary': 'Accept-Encoding, Accept, X-Requested-With', 'server': 'github.com', 'x-github-request-id': 'B4F4:3F00B5:1EA8EA8:1FBB75B:68321E71'}
2025-05-25 03:30:56,185 [INFO] 8c057060-bbaa-4087-adfa-12a8078effff 请求: GET https://api.github.com/copilot_internal/v2/token
2025-05-25 03:30:56,187 [INFO] 8c057060-bbaa-4087-adfa-12a8078effff 请求头: {'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ****************************************', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.2 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'vscode-file://vscode-app', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN', 'priority': 'u=1, i'}
2025-05-25 03:30:56,194 [INFO] f70d6b65-4f6c-4226-87d3-f3f29f6302fe 请求: GET https://api.github.com/copilot_internal/user
2025-05-25 03:30:56,195 [INFO] f70d6b65-4f6c-4226-87d3-f3f29f6302fe 请求头: {'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ****************************************', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.2 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'vscode-file://vscode-app', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN', 'priority': 'u=1, i'}
2025-05-25 03:30:56,481 [INFO] fe354bd5-c892-40d3-aba0-9611008b3e75 响应耗时: 0.316s 200 https://api.github.com/copilot_internal/user
2025-05-25 03:30:56,483 [INFO] fe354bd5-c892-40d3-aba0-9611008b3e75 响应头: {'date': 'Sat, 24 May 2025 19:30:57 GMT', 'content-type': 'application/json; charset=utf-8', 'cache-control': 'private, max-age=60, s-maxage=60', 'vary': 'Accept, Authorization, Cookie, X-GitHub-OTP,Accept-Encoding, Accept, X-Requested-With', 'etag': 'W/"bfc4b0c4844fec5a1f4b5161df3590f81d7bff3ffa1453f10069bd458ae6673b"', 'x-oauth-scopes': 'user:email', 'x-accepted-oauth-scopes': '', 'x-oauth-client-id': '01ab8ac9400c4e429b23', 'x-github-media-type': 'github.v3; format=json', 'x-ratelimit-limit': '5000', 'x-ratelimit-remaining': '4949', 'x-ratelimit-reset': '1748116545', 'x-ratelimit-used': '51', 'x-ratelimit-resource': 'core', 'access-control-expose-headers': 'ETag, Link, Location, Retry-After, X-GitHub-OTP, X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Used, X-RateLimit-Resource, X-RateLimit-Reset, X-OAuth-Scopes, X-Accepted-OAuth-Scopes, X-Poll-Interval, X-GitHub-Media-Type, X-GitHub-SSO, X-GitHub-Request-Id, Deprecation, Sunset', 'access-control-allow-origin': '*', 'strict-transport-security': 'max-age=31536000; includeSubdomains; preload', 'x-frame-options': 'deny', 'x-content-type-options': 'nosniff', 'x-xss-protection': '0', 'referrer-policy': 'origin-when-cross-origin, strict-origin-when-cross-origin', 'content-security-policy': "default-src 'none'", 'content-encoding': 'gzip', 'server': 'github.com', 'x-github-request-id': 'B4F4:3F00B5:1EA8F61:1FBB82B:68321E71'}
2025-05-25 03:30:56,485 [INFO] fe354bd5-c892-40d3-aba0-9611008b3e75 响应体: {
  "access_type_sku": "trial_30_yearly_subscriber",
  "analytics_tracking_id": "febcf6966fc284dd7bf1f83a45eb9c1a",
  "assigned_date": null,
  "can_signup_for_limited": false,
  "chat_enabled": true,
  "copilot_plan": "individual",
  "organization_login_list": [

  ],
  "organization_list": [

  ]
}

2025-05-25 03:30:56,560 [INFO] f70d6b65-4f6c-4226-87d3-f3f29f6302fe 响应耗时: 0.364s 200 https://api.github.com/copilot_internal/user
2025-05-25 03:30:56,568 [INFO] f70d6b65-4f6c-4226-87d3-f3f29f6302fe 响应头: {'date': 'Sat, 24 May 2025 19:30:57 GMT', 'content-type': 'application/json; charset=utf-8', 'cache-control': 'private, max-age=60, s-maxage=60', 'vary': 'Accept, Authorization, Cookie, X-GitHub-OTP,Accept-Encoding, Accept, X-Requested-With', 'etag': 'W/"bfc4b0c4844fec5a1f4b5161df3590f81d7bff3ffa1453f10069bd458ae6673b"', 'x-oauth-scopes': 'user:email', 'x-accepted-oauth-scopes': '', 'x-oauth-client-id': '01ab8ac9400c4e429b23', 'x-github-media-type': 'github.v3; format=json', 'x-ratelimit-limit': '5000', 'x-ratelimit-remaining': '4948', 'x-ratelimit-reset': '1748116545', 'x-ratelimit-used': '52', 'x-ratelimit-resource': 'core', 'access-control-expose-headers': 'ETag, Link, Location, Retry-After, X-GitHub-OTP, X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Used, X-RateLimit-Resource, X-RateLimit-Reset, X-OAuth-Scopes, X-Accepted-OAuth-Scopes, X-Poll-Interval, X-GitHub-Media-Type, X-GitHub-SSO, X-GitHub-Request-Id, Deprecation, Sunset', 'access-control-allow-origin': '*', 'strict-transport-security': 'max-age=31536000; includeSubdomains; preload', 'x-frame-options': 'deny', 'x-content-type-options': 'nosniff', 'x-xss-protection': '0', 'referrer-policy': 'origin-when-cross-origin, strict-origin-when-cross-origin', 'content-security-policy': "default-src 'none'", 'content-encoding': 'gzip', 'server': 'github.com', 'x-github-request-id': 'B4F4:3F00B5:1EA8F81:1FBB853:68321E71'}
2025-05-25 03:30:56,578 [INFO] f70d6b65-4f6c-4226-87d3-f3f29f6302fe 响应体: {
  "access_type_sku": "trial_30_yearly_subscriber",
  "analytics_tracking_id": "febcf6966fc284dd7bf1f83a45eb9c1a",
  "assigned_date": null,
  "can_signup_for_limited": false,
  "chat_enabled": true,
  "copilot_plan": "individual",
  "organization_login_list": [

  ],
  "organization_list": [

  ]
}

2025-05-25 03:30:56,605 [INFO] 8c057060-bbaa-4087-adfa-12a8078effff 响应耗时: 0.417s 200 https://api.github.com/copilot_internal/v2/token
2025-05-25 03:30:56,614 [INFO] 8c057060-bbaa-4087-adfa-12a8078effff 响应头: {'date': 'Sat, 24 May 2025 19:30:57 GMT', 'content-type': 'application/json; charset=utf-8', 'cache-control': 'private, max-age=60, s-maxage=60', 'vary': 'Accept, Authorization, Cookie, X-GitHub-OTP,Accept-Encoding, Accept, X-Requested-With', 'etag': 'W/"61cd71f427f91b165fd5d8c6719b3946325b820b7b11bbf10f4e0237cef06899"', 'x-oauth-scopes': 'user:email', 'x-accepted-oauth-scopes': '', 'x-oauth-client-id': '01ab8ac9400c4e429b23', 'x-github-media-type': 'github.v3; format=json', 'x-ratelimit-limit': '5000', 'x-ratelimit-remaining': '4947', 'x-ratelimit-reset': '1748116545', 'x-ratelimit-used': '53', 'x-ratelimit-resource': 'core', 'access-control-expose-headers': 'ETag, Link, Location, Retry-After, X-GitHub-OTP, X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Used, X-RateLimit-Resource, X-RateLimit-Reset, X-OAuth-Scopes, X-Accepted-OAuth-Scopes, X-Poll-Interval, X-GitHub-Media-Type, X-GitHub-SSO, X-GitHub-Request-Id, Deprecation, Sunset', 'access-control-allow-origin': '*', 'strict-transport-security': 'max-age=31536000; includeSubdomains; preload', 'x-frame-options': 'deny', 'x-content-type-options': 'nosniff', 'x-xss-protection': '0', 'referrer-policy': 'origin-when-cross-origin, strict-origin-when-cross-origin', 'content-security-policy': "default-src 'none'", 'content-encoding': 'gzip', 'server': 'github.com', 'x-github-request-id': 'B4F4:3F00B5:1EA8F7B:1FBB84E:68321E71'}
2025-05-25 03:30:56,623 [INFO] 8c057060-bbaa-4087-adfa-12a8078effff 响应体: {
  "annotations_enabled": true,
  "chat_enabled": true,
  "chat_jetbrains_enabled": true,
  "code_quote_enabled": true,
  "code_review_enabled": true,
  "codesearch": true,
  "copilotignore_enabled": false,
  "endpoints": {
    "api": "https://api.individual.githubcopilot.com",
    "origin-tracker": "https://origin-tracker.individual.githubcopilot.com",
    "proxy": "https://proxy.individual.githubcopilot.com",
    "telemetry": "https://telemetry.individual.githubcopilot.com"
  },
  "expires_at": 1748116857,
  "individual": true,
  "limited_user_quotas": null,
  "limited_user_reset_date": null,
  "prompt_8k": true,
  "public_suggestions": "disabled",
  "refresh_in": 1500,
  "sku": "trial_30_yearly_subscriber",
  "snippy_load_test_enabled": false,
  "telemetry": "enabled",
  "token": "tid=febcf6966fc284dd7bf1f83a45eb9c1a;exp=1748116857;sku=trial_30_yearly_subscriber;proxy-ep=proxy.individual.githubcopilot.com;st=dotcom;chat=1;cit=1;malfil=1;editor_preview_features=1;ccr=1;rt=1;8kp=1;ip=***************;asn=AS25820:35b6a54437a7cb1a223cfae8162bbcc76f059337ffd4b920c54f877e2d8caa13",
  "tracking_id": "febcf6966fc284dd7bf1f83a45eb9c1a",
  "vsc_electron_fetcher_v2": false,
  "xcode": true,
  "xcode_chat": false
}

2025-05-25 03:30:59,039 [INFO] d509a665-7879-4dde-a1f5-550e45433609 请求: OPTIONS https://api.github.com/copilot_internal/user
2025-05-25 03:30:59,044 [INFO] d509a665-7879-4dde-a1f5-550e45433609 请求头: {'accept': '*/*', 'access-control-request-method': 'GET', 'access-control-request-headers': 'authorization', 'origin': 'vscode-file://vscode-app', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.2 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'sec-fetch-mode': 'cors', 'sec-fetch-site': 'cross-site', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN', 'priority': 'u=1, i'}
2025-05-25 03:30:59,310 [INFO] d509a665-7879-4dde-a1f5-550e45433609 响应耗时: 0.263s 204 https://api.github.com/copilot_internal/user
2025-05-25 03:30:59,312 [INFO] d509a665-7879-4dde-a1f5-550e45433609 响应头: {'date': 'Sat, 24 May 2025 19:31:00 GMT', 'access-control-expose-headers': 'ETag, Link, Location, Retry-After, X-GitHub-OTP, X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Used, X-RateLimit-Resource, X-RateLimit-Reset, X-OAuth-Scopes, X-Accepted-OAuth-Scopes, X-Poll-Interval, X-GitHub-Media-Type, X-GitHub-SSO, X-GitHub-Request-Id, Deprecation, Sunset', 'access-control-max-age': '86400', 'access-control-allow-headers': 'Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match, If-Unmodified-Since, Accept-Encoding, X-GitHub-OTP, X-Requested-With, User-Agent, GraphQL-Features, X-Github-Next-Global-ID, X-GitHub-Api-Version, X-Fetch-Nonce', 'access-control-allow-methods': 'GET, POST, PATCH, PUT, DELETE', 'access-control-allow-origin': '*', 'strict-transport-security': 'max-age=31536000; includeSubdomains; preload', 'x-frame-options': 'deny', 'x-content-type-options': 'nosniff', 'x-xss-protection': '0', 'referrer-policy': 'origin-when-cross-origin, strict-origin-when-cross-origin', 'content-security-policy': "default-src 'none'; base-uri 'self'; child-src github.githubassets.com github.com/assets-cdn/worker/ github.com/assets/ gist.github.com/assets-cdn/worker/; connect-src 'self' uploads.github.com www.githubstatus.com collector.github.com raw.githubusercontent.com api.github.com github-cloud.s3.amazonaws.com github-production-repository-file-5c1aeb.s3.amazonaws.com github-production-upload-manifest-file-7fdce7.s3.amazonaws.com github-production-user-asset-6210df.s3.amazonaws.com *.rel.tunnels.api.visualstudio.com wss://*.rel.tunnels.api.visualstudio.com objects-origin.githubusercontent.com copilot-proxy.githubusercontent.com proxy.individual.githubcopilot.com proxy.business.githubcopilot.com proxy.enterprise.githubcopilot.com; font-src github.githubassets.com; form-action 'self' github.com gist.github.com copilot-workspace.githubnext.com objects-origin.githubusercontent.com; frame-ancestors 'none'; frame-src viewscreen.githubusercontent.com notebooks.githubusercontent.com; img-src 'self' data: blob: github.githubassets.com media.githubusercontent.com camo.githubusercontent.com identicons.github.com avatars.githubusercontent.com private-avatars.githubusercontent.com github-cloud.s3.amazonaws.com objects.githubusercontent.com release-assets.githubusercontent.com secured-user-images.githubusercontent.com/ user-images.githubusercontent.com/ private-user-images.githubusercontent.com opengraph.githubassets.com copilotprodattachments.blob.core.windows.net/github-production-copilot-attachments/ github-production-user-asset-6210df.s3.amazonaws.com customer-stories-feed.github.com spotlights-feed.github.com objects-origin.githubusercontent.com; manifest-src 'self'; media-src github.com user-images.githubusercontent.com/ secured-user-images.githubusercontent.com/ private-user-images.githubusercontent.com github-production-user-asset-6210df.s3.amazonaws.com gist.github.com; script-src github.githubassets.com; style-src 'unsafe-inline' github.githubassets.com; upgrade-insecure-requests; worker-src github.githubassets.com github.com/assets-cdn/worker/ github.com/assets/ gist.github.com/assets-cdn/worker/", 'vary': 'Accept-Encoding, Accept, X-Requested-With', 'server': 'github.com', 'x-github-request-id': 'B4F4:3F00B5:1EA985A:1FBC171:68321E71'}
2025-05-25 03:30:59,354 [INFO] 5fc6a855-4540-47b8-ad53-07b0d9f626b5 请求: GET https://api.github.com/copilot_internal/user
2025-05-25 03:30:59,362 [INFO] 5fc6a855-4540-47b8-ad53-07b0d9f626b5 请求头: {'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ****************************************', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.2 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'vscode-file://vscode-app', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN', 'priority': 'u=1, i'}
2025-05-25 03:30:59,741 [INFO] 5fc6a855-4540-47b8-ad53-07b0d9f626b5 响应耗时: 0.378s 200 https://api.github.com/copilot_internal/user
2025-05-25 03:30:59,744 [INFO] 5fc6a855-4540-47b8-ad53-07b0d9f626b5 响应头: {'date': 'Sat, 24 May 2025 19:31:01 GMT', 'content-type': 'application/json; charset=utf-8', 'cache-control': 'private, max-age=60, s-maxage=60', 'vary': 'Accept, Authorization, Cookie, X-GitHub-OTP,Accept-Encoding, Accept, X-Requested-With', 'etag': 'W/"bfc4b0c4844fec5a1f4b5161df3590f81d7bff3ffa1453f10069bd458ae6673b"', 'x-oauth-scopes': 'user:email', 'x-accepted-oauth-scopes': '', 'x-oauth-client-id': '01ab8ac9400c4e429b23', 'x-github-media-type': 'github.v3; format=json', 'x-ratelimit-limit': '5000', 'x-ratelimit-remaining': '4946', 'x-ratelimit-reset': '1748116545', 'x-ratelimit-used': '54', 'x-ratelimit-resource': 'core', 'access-control-expose-headers': 'ETag, Link, Location, Retry-After, X-GitHub-OTP, X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Used, X-RateLimit-Resource, X-RateLimit-Reset, X-OAuth-Scopes, X-Accepted-OAuth-Scopes, X-Poll-Interval, X-GitHub-Media-Type, X-GitHub-SSO, X-GitHub-Request-Id, Deprecation, Sunset', 'access-control-allow-origin': '*', 'strict-transport-security': 'max-age=31536000; includeSubdomains; preload', 'x-frame-options': 'deny', 'x-content-type-options': 'nosniff', 'x-xss-protection': '0', 'referrer-policy': 'origin-when-cross-origin, strict-origin-when-cross-origin', 'content-security-policy': "default-src 'none'", 'content-encoding': 'gzip', 'server': 'github.com', 'x-github-request-id': 'B4F4:3F00B5:1EA9954:1FBC28B:68321E74'}
2025-05-25 03:30:59,745 [INFO] 5fc6a855-4540-47b8-ad53-07b0d9f626b5 响应体: {
  "access_type_sku": "trial_30_yearly_subscriber",
  "analytics_tracking_id": "febcf6966fc284dd7bf1f83a45eb9c1a",
  "assigned_date": null,
  "can_signup_for_limited": false,
  "chat_enabled": true,
  "copilot_plan": "individual",
  "organization_login_list": [

  ],
  "organization_list": [

  ]
}

2025-05-25 03:31:01,774 [INFO] bd4ce27a-25d2-4f69-95b4-27919b90b9b6 请求: GET https://api.github.com/copilot_internal/v2/token
2025-05-25 03:31:01,776 [INFO] bd4ce27a-25d2-4f69-95b4-27919b90b9b6 请求头: {'authorization': 'Bearer ****************************************', 'editor-version': 'vscode/1.100.2', 'editor-plugin-version': 'copilot/1.323.0', 'copilot-language-server-version': '1.323.0', 'x-github-api-version': '2025-05-01', 'user-agent': 'GithubCopilot/1.323.0', 'accept': '*/*', 'accept-encoding': 'gzip,deflate,br'}
2025-05-25 03:31:02,026 [INFO] 771abaa7-269b-4ae9-b008-d43dfa394f17 请求: GET https://api.github.com/copilot_internal/v2/token
2025-05-25 03:31:02,027 [INFO] 771abaa7-269b-4ae9-b008-d43dfa394f17 请求头: {'authorization': 'token ****************************************', 'editor-plugin-version': 'copilot-chat/0.27.2', 'editor-version': 'vscode/1.100.2', 'user-agent': 'GitHubCopilotChat/0.27.2', 'x-github-api-version': '2025-04-01', 'x-vscode-user-agent-library-version': 'electron-fetch', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'no-cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'priority': 'u=4, i'}
2025-05-25 03:31:02,147 [INFO] bd4ce27a-25d2-4f69-95b4-27919b90b9b6 响应耗时: 0.370s 200 https://api.github.com/copilot_internal/v2/token
2025-05-25 03:31:02,149 [INFO] bd4ce27a-25d2-4f69-95b4-27919b90b9b6 响应头: {'date': 'Sat, 24 May 2025 19:31:03 GMT', 'content-type': 'application/json; charset=utf-8', 'cache-control': 'private, max-age=60, s-maxage=60', 'vary': 'Accept, Authorization, Cookie, X-GitHub-OTP,Accept-Encoding, Accept, X-Requested-With', 'etag': 'W/"5d173d1166ae4537420baf9eb4d0a0c016756576f6a0f45c880c29971d4f842d"', 'x-oauth-scopes': 'read:user, repo, user:email, workflow', 'x-accepted-oauth-scopes': '', 'x-oauth-client-id': '01ab8ac9400c4e429b23', 'x-github-media-type': 'github.v3; format=json', 'x-ratelimit-limit': '5000', 'x-ratelimit-remaining': '4945', 'x-ratelimit-reset': '1748116545', 'x-ratelimit-used': '55', 'x-ratelimit-resource': 'core', 'access-control-expose-headers': 'ETag, Link, Location, Retry-After, X-GitHub-OTP, X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Used, X-RateLimit-Resource, X-RateLimit-Reset, X-OAuth-Scopes, X-Accepted-OAuth-Scopes, X-Poll-Interval, X-GitHub-Media-Type, X-GitHub-SSO, X-GitHub-Request-Id, Deprecation, Sunset', 'access-control-allow-origin': '*', 'strict-transport-security': 'max-age=31536000; includeSubdomains; preload', 'x-frame-options': 'deny', 'x-content-type-options': 'nosniff', 'x-xss-protection': '0', 'referrer-policy': 'origin-when-cross-origin, strict-origin-when-cross-origin', 'content-security-policy': "default-src 'none'", 'content-encoding': 'gzip', 'server': 'github.com', 'x-github-request-id': 'B51C:281694:1D24059:1E369CC:68321E76'}
2025-05-25 03:31:02,151 [INFO] bd4ce27a-25d2-4f69-95b4-27919b90b9b6 响应体: {"annotations_enabled":true,"chat_enabled":true,"chat_jetbrains_enabled":true,"code_quote_enabled":true,"code_review_enabled":true,"codesearch":true,"copilotignore_enabled":false,"endpoints":{"api":"https://api.individual.githubcopilot.com","origin-tracker":"https://origin-tracker.individual.githubcopilot.com","proxy":"https://proxy.individual.githubcopilot.com","telemetry":"https://telemetry.individual.githubcopilot.com"},"expires_at":1748116863,"individual":true,"limited_user_quotas":null,"limited_user_reset_date":null,"prompt_8k":true,"public_suggestions":"disabled","refresh_in":1500,"sku":"trial_30_yearly_subscriber","snippy_load_test_enabled":false,"telemetry":"enabled","token":"tid=febcf6966fc284dd7bf1f83a45eb9c1a;exp=1748116863;sku=trial_30_yearly_subscriber;proxy-ep=proxy.individual.githubcopilot.com;st=dotcom;chat=1;cit=1;malfil=1;editor_preview_features=1;ccr=1;rt=1;8kp=1;ip=***************;asn=AS25820:53c26e18fdb3a3e017194fecc3151dd8c25d368ea59c4d9bcf5d0c762f87a534","tracking_id":"febcf6966fc284dd7bf1f83a45eb9c1a","vsc_electron_fetcher_v2":false,"xcode":true,"xcode_chat":false}
2025-05-25 03:31:02,386 [INFO] 771abaa7-269b-4ae9-b008-d43dfa394f17 响应耗时: 0.358s 200 https://api.github.com/copilot_internal/v2/token
2025-05-25 03:31:02,390 [INFO] 771abaa7-269b-4ae9-b008-d43dfa394f17 响应头: {'date': 'Sat, 24 May 2025 19:31:03 GMT', 'content-type': 'application/json; charset=utf-8', 'cache-control': 'private, max-age=60, s-maxage=60', 'vary': 'Accept, Authorization, Cookie, X-GitHub-OTP,Accept-Encoding, Accept, X-Requested-With', 'etag': 'W/"fc27bbe7feaedbd8587e345c4fd95d52dcca3a1bdf41e033af70be5d2a6aaffd"', 'x-oauth-scopes': 'read:user, repo, user:email, workflow', 'x-accepted-oauth-scopes': '', 'x-oauth-client-id': '01ab8ac9400c4e429b23', 'x-github-media-type': 'github.v3; format=json', 'x-ratelimit-limit': '5000', 'x-ratelimit-remaining': '4944', 'x-ratelimit-reset': '1748116545', 'x-ratelimit-used': '56', 'x-ratelimit-resource': 'core', 'access-control-expose-headers': 'ETag, Link, Location, Retry-After, X-GitHub-OTP, X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Used, X-RateLimit-Resource, X-RateLimit-Reset, X-OAuth-Scopes, X-Accepted-OAuth-Scopes, X-Poll-Interval, X-GitHub-Media-Type, X-GitHub-SSO, X-GitHub-Request-Id, Deprecation, Sunset', 'access-control-allow-origin': '*', 'strict-transport-security': 'max-age=31536000; includeSubdomains; preload', 'x-frame-options': 'deny', 'x-content-type-options': 'nosniff', 'x-xss-protection': '0', 'referrer-policy': 'origin-when-cross-origin, strict-origin-when-cross-origin', 'content-security-policy': "default-src 'none'", 'content-encoding': 'gzip', 'server': 'github.com', 'x-github-request-id': 'B524:F5198:1E7CC00:1F8F789:68321E77'}
2025-05-25 03:31:02,395 [INFO] 771abaa7-269b-4ae9-b008-d43dfa394f17 响应体: {"annotations_enabled":true,"chat_enabled":true,"chat_jetbrains_enabled":true,"code_quote_enabled":true,"code_review_enabled":true,"codesearch":true,"copilotignore_enabled":false,"endpoints":{"api":"https://api.individual.githubcopilot.com","origin-tracker":"https://origin-tracker.individual.githubcopilot.com","proxy":"https://proxy.individual.githubcopilot.com","telemetry":"https://telemetry.individual.githubcopilot.com"},"expires_at":1748116863,"individual":true,"limited_user_quotas":null,"limited_user_reset_date":null,"prompt_8k":true,"public_suggestions":"disabled","refresh_in":1500,"sku":"trial_30_yearly_subscriber","snippy_load_test_enabled":false,"telemetry":"enabled","token":"tid=febcf6966fc284dd7bf1f83a45eb9c1a;exp=1748116863;sku=trial_30_yearly_subscriber;proxy-ep=proxy.individual.githubcopilot.com;st=dotcom;chat=1;cit=1;malfil=1;editor_preview_features=1;ccr=1;rt=1;8kp=1;ip=***************;asn=AS25820:53c26e18fdb3a3e017194fecc3151dd8c25d368ea59c4d9bcf5d0c762f87a534","tracking_id":"febcf6966fc284dd7bf1f83a45eb9c1a","vsc_electron_fetcher_v2":false,"xcode":true,"xcode_chat":false}
2025-05-25 03:31:02,407 [INFO] aa766abc-9b08-4f56-9199-c3b02ca89f39 请求: GET https://api.github.com/copilot_internal/user
2025-05-25 03:31:02,411 [INFO] aa766abc-9b08-4f56-9199-c3b02ca89f39 请求头: {'authorization': 'token ****************************************', 'editor-plugin-version': 'copilot-chat/0.27.2', 'editor-version': 'vscode/1.100.2', 'user-agent': 'GitHubCopilotChat/0.27.2', 'x-github-api-version': '2025-04-01', 'x-vscode-user-agent-library-version': 'electron-fetch', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'no-cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'priority': 'u=4, i'}
2025-05-25 03:31:02,728 [INFO] aa766abc-9b08-4f56-9199-c3b02ca89f39 响应耗时: 0.316s 200 https://api.github.com/copilot_internal/user
2025-05-25 03:31:02,730 [INFO] aa766abc-9b08-4f56-9199-c3b02ca89f39 响应头: {'date': 'Sat, 24 May 2025 19:31:04 GMT', 'content-type': 'application/json; charset=utf-8', 'cache-control': 'private, max-age=60, s-maxage=60', 'vary': 'Accept, Authorization, Cookie, X-GitHub-OTP,Accept-Encoding, Accept, X-Requested-With', 'etag': 'W/"c80925dcedc14f5365cab016804a33422a9301b60fbb52f61d412cdbf2288c23"', 'x-oauth-scopes': 'read:user, repo, user:email, workflow', 'x-accepted-oauth-scopes': '', 'x-oauth-client-id': '01ab8ac9400c4e429b23', 'x-github-media-type': 'github.v3; format=json', 'x-ratelimit-limit': '5000', 'x-ratelimit-remaining': '4943', 'x-ratelimit-reset': '1748116545', 'x-ratelimit-used': '57', 'x-ratelimit-resource': 'core', 'access-control-expose-headers': 'ETag, Link, Location, Retry-After, X-GitHub-OTP, X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Used, X-RateLimit-Resource, X-RateLimit-Reset, X-OAuth-Scopes, X-Accepted-OAuth-Scopes, X-Poll-Interval, X-GitHub-Media-Type, X-GitHub-SSO, X-GitHub-Request-Id, Deprecation, Sunset', 'access-control-allow-origin': '*', 'strict-transport-security': 'max-age=31536000; includeSubdomains; preload', 'x-frame-options': 'deny', 'x-content-type-options': 'nosniff', 'x-xss-protection': '0', 'referrer-policy': 'origin-when-cross-origin, strict-origin-when-cross-origin', 'content-security-policy': "default-src 'none'", 'content-encoding': 'gzip', 'server': 'github.com', 'x-github-request-id': 'B524:F5198:1E7CD47:1F8F8D6:68321E77'}
2025-05-25 03:31:02,732 [INFO] aa766abc-9b08-4f56-9199-c3b02ca89f39 响应体: {"access_type_sku":"trial_30_yearly_subscriber","analytics_tracking_id":"febcf6966fc284dd7bf1f83a45eb9c1a","assigned_date":null,"can_signup_for_limited":false,"chat_enabled":true,"copilot_plan":"individual","organization_login_list":[],"organization_list":[]}
2025-05-25 03:31:02,743 [INFO] 604768b9-5556-4f03-8123-d3d831eda27e 请求: GET https://api.github.com/user
2025-05-25 03:31:02,744 [INFO] 604768b9-5556-4f03-8123-d3d831eda27e 请求头: {'accept': 'application/vnd.github+json', 'authorization': 'Bearer ****************************************', 'user-agent': 'GitHubCopilotChat/0.27.2', 'x-github-api-version': '2022-11-28', 'x-vscode-user-agent-library-version': 'electron-fetch', 'sec-fetch-site': 'none', 'sec-fetch-mode': 'no-cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'priority': 'u=4, i'}
