#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志分析脚本 - 识别关键信息和次要信息
"""

import re
from pathlib import Path

def analyze_log_patterns(log_file_path):
    """分析日志文件中的模式和频率"""

    with open(log_file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    lines = content.split('\n')
    total_lines = len(lines)

    print(f"总行数: {total_lines}")
    print(f"文件大小: {len(content.encode('utf-8')) / 1024:.1f} KB")
    print("=" * 50)

    # 1. 分析请求头字段频率
    print("1. 请求头字段分析:")
    header_patterns = {
        'host': r"'host': '[^']*'",
        'connection': r"'connection': '[^']*'",
        'Content-Type': r"'Content-Type': '[^']*'",
        'User-Agent': r"'User-Agent': '[^']*'",
        'x-request-id': r"'x-request-id': '[^']*'",
        'x-request-session-id': r"'x-request-session-id': '[^']*'",
        'x-api-version': r"'x-api-version': '[^']*'",
        'Authorization': r"'Authorization': '[^']*'",
        'accept': r"'accept': '[^']*'",
        'accept-language': r"'accept-language': '[^']*'",
        'sec-fetch-mode': r"'sec-fetch-mode': '[^']*'",
        'accept-encoding': r"'accept-encoding': '[^']*'",
        'content-length': r"'content-length': '[^']*'"
    }

    for field, pattern in header_patterns.items():
        matches = re.findall(pattern, content)
        print(f"  {field}: {len(matches)} 次")

    print("\n" + "=" * 50)

    # 2. 分析长字符串模式
    print("2. 长字符串模式分析:")
    long_string_patterns = {
        'blob_name (64字符哈希)': r'"blob_name":"[a-f0-9]{64}"',
        'request_id (UUID)': r'"x-request-id": \'[a-f0-9-]{36}\'',
        'session_id (UUID)': r'"x-request-session-id": \'[a-f0-9-]{36}\'',
        'Authorization token': r'"Authorization": \'Bearer [a-f0-9]{64}\'',
        'User-Agent完整字符串': r'"User-Agent": \'Augment\.vscode-augment/[^\']*\''
    }

    for desc, pattern in long_string_patterns.items():
        matches = re.findall(pattern, content)
        print(f"  {desc}: {len(matches)} 次")

    print("\n" + "=" * 50)

    # 3. 分析API端点
    print("3. API端点分析:")
    api_patterns = {
        'subscription-info': r'https://d16\.api\.augmentcode\.com/subscription-info',
        'memorize': r'https://d16\.api\.augmentcode\.com/memorize',
        'next-edit-stream': r'https://d16\.api\.augmentcode\.com/next-edit-stream',
        'find-missing': r'https://d16\.api\.augmentcode\.com/find-missing',
        'completion': r'https://d16\.api\.augmentcode\.com/completion',
        'record-session-events': r'https://d16\.api\.augmentcode\.com/record-session-events',
        'client-metrics': r'https://d16\.api\.augmentcode\.com/client-metrics',
        'batch-upload': r'https://d16\.api\.augmentcode\.com/batch-upload',
        'chat-stream': r'https://d16\.api\.augmentcode\.com/chat-stream'
    }

    for endpoint, pattern in api_patterns.items():
        matches = re.findall(pattern, content)
        print(f"  {endpoint}: {len(matches)} 次")

    print("\n" + "=" * 50)

    # 4. 分析时间戳模式
    print("4. 时间戳分析:")
    timestamp_patterns = {
        '时间戳 (HH:MM:SS,mmm)': r'\d{2}:\d{2}:\d{2},\d{3}',
        'ISO时间戳': r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z',
        'Unix时间戳(秒)': r'"event_time_sec":\d+',
        'Unix时间戳(纳秒)': r'"event_time_nsec":\d+'
    }

    for desc, pattern in timestamp_patterns.items():
        matches = re.findall(pattern, content)
        print(f"  {desc}: {len(matches)} 次")

    print("\n" + "=" * 50)

    # 5. 分析JSON结构
    print("5. JSON结构分析:")
    json_patterns = {
        '请求体': r'请求体: \{.*?\}(?=\n\d{2}:\d{2}:\d{2})',
        '响应体': r'响应体: \{.*?\}(?=\n\d{2}:\d{2}:\d{2})',
        '请求头': r'请求头: \{.*?\}(?=\n\d{2}:\d{2}:\d{2})',
        '响应头': r'响应头: \{.*?\}(?=\n\d{2}:\d{2}:\d{2})'
    }

    for desc, pattern in json_patterns.items():
        matches = re.findall(pattern, content, re.DOTALL)
        total_chars = sum(len(match) for match in matches)
        print(f"  {desc}: {len(matches)} 个, 总字符数: {total_chars}")

def create_compressed_log(log_file_path, output_path):
    """创建压缩版本的日志"""
    with open(log_file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    print("\n" + "=" * 50)
    print("开始压缩日志...")

    # 定义替换规则
    replacements = [
        # 1. 简化重复的请求头字段
        (r"'host': 'd16\.api\.augmentcode\.com'", "'host': 'API'"),
        (r"'connection': 'keep-alive'", "'conn': 'alive'"),
        (r"'Content-Type': 'application/json'", "'type': 'json'"),
        (r"'User-Agent': 'Augment\.vscode-augment/0\.458\.1 \(win32; x64; 10\.0\.22000\) vscode/1\.100\.2'", "'ua': 'augment'"),
        (r"'accept': '\*/\*'", "'accept': '*'"),
        (r"'accept-language': '\*'", "'lang': '*'"),
        (r"'sec-fetch-mode': 'cors'", "'mode': 'cors'"),
        (r"'accept-encoding': 'br, gzip, deflate'", "'enc': 'br,gz,def'"),

        # 2. 简化API端点
        (r"https://d16\.api\.augmentcode\.com/", "API/"),

        # 3. 缩短UUID和token (保留前8位)
        (r"'x-request-id': '([a-f0-9]{8})[a-f0-9-]{28}'", r"'req_id': '\1'"),
        (r"'x-request-session-id': '([a-f0-9]{8})[a-f0-9-]{28}'", r"'sess_id': '\1'"),
        (r"'Authorization': 'Bearer ([a-f0-9]{8})[a-f0-9]{56}'", r"'auth': '\1'"),

        # 4. 简化blob_name (保留前8位)
        (r'"blob_name":"([a-f0-9]{8})[a-f0-9]{56}"', r'"blob":"\1"'),

        # 5. 简化时间戳 (移除毫秒)
        (r"(\d{2}:\d{2}:\d{2}),\d{3}", r"\1"),

        # 6. 简化响应头中的重复信息
        (r"'Content-Length': '\d+'", "'len': 'N'"),
        (r"'content-type': 'application/json'", "'type': 'json'"),
        (r"'date': '[^']*'", "'date': 'DATE'"),
        (r"'Via': '1\.1 google'", "'via': 'google'"),
        (r"'Alt-Svc': '[^']*'", "'alt': 'h3'"),

        # 7. 移除一些不重要的字段
        (r", 'x-api-version': '[^']*'", ""),
        (r", 'Transfer-Encoding': 'chunked'", ""),
    ]

    compressed = content
    original_size = len(content.encode('utf-8'))

    for pattern, replacement in replacements:
        before_count = len(re.findall(pattern, compressed))
        compressed = re.sub(pattern, replacement, compressed)
        if before_count > 0:
            print(f"  替换 '{pattern[:30]}...': {before_count} 次")

    # 保存压缩后的文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(compressed)

    compressed_size = len(compressed.encode('utf-8'))
    compression_ratio = (1 - compressed_size / original_size) * 100

    print(f"\n压缩完成:")
    print(f"  原始大小: {original_size / 1024:.1f} KB")
    print(f"  压缩后大小: {compressed_size / 1024:.1f} KB")
    print(f"  压缩率: {compression_ratio:.1f}%")
    print(f"  输出文件: {output_path}")

def suggest_compression_strategies():
    """建议压缩策略"""
    print("\n" + "=" * 50)
    print("基于分析的关键发现:")

    findings = [
        "• 请求头字段高度重复 (47次相同字段)",
        "• 请求体占用最多空间 (126KB/180KB = 70%)",
        "• UUID和token很长但信息密度低",
        "• 时间戳精度过高 (毫秒级277次)",
        "• API端点重复度高",
        "• 响应头信息大多是元数据"
    ]

    for finding in findings:
        print(f"  {finding}")

if __name__ == "__main__":
    log_file = "logs/3399157.small.log"
    compressed_file = "logs/3399157.compressed.log"

    if Path(log_file).exists():
        analyze_log_patterns(log_file)
        suggest_compression_strategies()
        create_compressed_log(log_file, compressed_file)
    else:
        print(f"文件 {log_file} 不存在")
