#!/usr/bin/env python3
"""
快速检查当前Python环境是否满足log-proxy工具的要求
"""
import sys
import os

def check_python_version():
    """检查Python版本"""
    print(f"🐍 Python版本: {sys.version}")
    major, minor = sys.version_info[:2]
    if major >= 3 and minor >= 8:
        print("✅ Python版本满足要求 (>= 3.8)")
        return True
    else:
        print("❌ Python版本过低，需要 >= 3.8")
        return False

def check_packages():
    """检查必需的包"""
    print("\n📦 检查必需的包:")
    packages = {
        'yaml': 'PyYAML',
        'mitmproxy': 'mitmproxy',
        'asyncio': '内置模块',
        'logging': '内置模块',
        'signal': '内置模块',
        'atexit': '内置模块'
    }
    
    all_ok = True
    for module, description in packages.items():
        try:
            __import__(module)
            print(f"✅ {module} ({description})")
        except ImportError:
            print(f"❌ {module} ({description}) - 未安装")
            all_ok = False
    
    return all_ok

def check_conda_env():
    """检查是否在conda环境中"""
    print("\n🔍 检查conda环境:")
    conda_prefix = os.environ.get('CONDA_PREFIX')
    conda_default_env = os.environ.get('CONDA_DEFAULT_ENV')
    
    if conda_prefix:
        print(f"✅ 当前conda环境: {conda_default_env or 'base'}")
        print(f"✅ 环境路径: {conda_prefix}")
        return True
    else:
        print("⚠️  未检测到conda环境")
        return False

def check_proxy_env():
    """检查当前代理环境变量"""
    print("\n🌐 当前代理环境变量:")
    http_proxy = os.environ.get('HTTP_PROXY')
    https_proxy = os.environ.get('HTTPS_PROXY')
    
    if http_proxy:
        print(f"HTTP_PROXY: {http_proxy}")
    else:
        print("HTTP_PROXY: 未设置")
    
    if https_proxy:
        print(f"HTTPS_PROXY: {https_proxy}")
    else:
        print("HTTPS_PROXY: 未设置")

def main():
    """主函数"""
    print("🔍 log-proxy工具环境检查")
    print("=" * 40)
    
    # 检查Python版本
    python_ok = check_python_version()
    
    # 检查包
    packages_ok = check_packages()
    
    # 检查conda环境
    conda_ok = check_conda_env()
    
    # 检查代理环境变量
    check_proxy_env()
    
    print("\n" + "=" * 40)
    if python_ok and packages_ok:
        print("🎉 环境检查通过！可以运行log-proxy工具")
        if conda_ok:
            print("💡 建议使用: run_with_conda.bat 或 ./run_with_conda.sh")
        else:
            print("💡 建议使用: python main.py")
    else:
        print("❌ 环境检查失败，请先解决上述问题")
        if not packages_ok:
            print("💡 运行 'python setup_conda_env.py' 来自动安装依赖")

if __name__ == "__main__":
    main()
