#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试压缩问题
"""

def compare_files(file1, file2):
    """比较两个文件的差异"""
    with open(file1, 'r', encoding='utf-8') as f1:
        content1 = f1.read()
    
    with open(file2, 'r', encoding='utf-8') as f2:
        content2 = f2.read()
    
    if content1 == content2:
        print("✅ 文件完全一致")
        return True
    
    print("❌ 文件不一致")
    print(f"原文件长度: {len(content1)}")
    print(f"解压文件长度: {len(content2)}")
    
    # 找到第一个不同的位置
    min_len = min(len(content1), len(content2))
    for i in range(min_len):
        if content1[i] != content2[i]:
            print(f"第一个差异位置: {i}")
            print(f"原文件字符: '{content1[i]}' (ord: {ord(content1[i])})")
            print(f"解压文件字符: '{content2[i]}' (ord: {ord(content2[i])})")
            
            # 显示周围的上下文
            start = max(0, i - 50)
            end = min(len(content1), i + 50)
            print(f"\n原文件上下文 [{start}:{end}]:")
            print(repr(content1[start:end]))
            
            end2 = min(len(content2), i + 50)
            print(f"\n解压文件上下文 [{start}:{end2}]:")
            print(repr(content2[start:end2]))
            break
    
    if len(content1) != len(content2):
        print(f"\n长度差异: {len(content1) - len(content2)}")
    
    return False

if __name__ == "__main__":
    original = "logs/3399157.small.log"
    decompressed = "logs/3399157.decompressed.log"
    
    compare_files(original, decompressed)
