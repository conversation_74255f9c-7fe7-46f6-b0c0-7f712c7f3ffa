#!/usr/bin/env python3
"""
手动测试脚本 - 运行程序并提供手动中断指导
"""
import subprocess
import sys
import time

def main():
    """主函数"""
    print("🚀 log-proxy手动测试")
    print("=" * 40)
    print("这个脚本将启动log-proxy程序")
    print("请在程序启动后按 Ctrl+C 来测试中断处理")
    print("观察程序是否能正确清理环境变量并退出")
    print()
    
    input("按回车键开始测试...")
    
    print("启动程序...")
    try:
        # 直接运行程序，让用户手动中断
        result = subprocess.run([sys.executable, "main.py"])
        print(f"程序退出，退出码: {result.returncode}")
    except KeyboardInterrupt:
        print("测试被用户中断")
    except Exception as e:
        print(f"运行出错: {e}")

if __name__ == "__main__":
    main()
