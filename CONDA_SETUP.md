# Conda环境配置指南

本文档详细说明如何在conda base环境中配置和运行log-proxy工具。

## 🎯 为什么使用conda base环境？

1. **环境隔离**: conda提供了良好的包管理和环境隔离
2. **依赖管理**: 自动处理包依赖关系，避免版本冲突
3. **跨平台**: 在Windows、Linux、macOS上都能一致运行
4. **稳定性**: base环境通常比较稳定，适合运行基础工具

## 📋 环境要求

- Python >= 3.8
- conda (Miniconda或Anaconda)
- 必需的Python包：
  - mitmproxy
  - PyYAML

## 🚀 快速开始

### 1. 检查环境
```bash
python check_env.py
```

### 2. 自动设置环境
```bash
python setup_conda_env.py
```

### 3. 运行工具
```bash
# Windows
run_with_conda.bat

# Linux/macOS
./run_with_conda.sh

# 或直接运行
python main.py
```

## 📁 文件说明

| 文件 | 用途 |
|------|------|
| `main.py` | 主程序文件 |
| `config.yaml` | 配置文件 |
| `check_env.py` | 环境检查脚本 |
| `setup_conda_env.py` | 自动环境设置脚本 |
| `test_env.py` | 环境变量管理功能测试 |
| `run_with_conda.bat` | Windows启动脚本 |
| `run_with_conda.sh` | Linux/macOS启动脚本 |

## 🔧 手动配置步骤

如果自动设置失败，可以手动配置：

### 1. 激活conda base环境
```bash
conda activate base
```

### 2. 安装依赖包
```bash
pip install mitmproxy pyyaml
```

### 3. 验证安装
```bash
python -c "import mitmproxy, yaml; print('所有包已正确安装')"
```

## 🐛 常见问题

### Q: conda命令不可用
**A**: 确保conda已正确安装并添加到PATH环境变量

### Q: 包安装失败
**A**: 尝试使用conda安装：
```bash
conda install -c conda-forge mitmproxy pyyaml
```

### Q: 权限错误
**A**: 在Windows上以管理员身份运行，或使用用户安装：
```bash
pip install --user mitmproxy pyyaml
```

### Q: 代理环境变量未生效
**A**: 确保程序正常启动和退出，检查是否有其他程序占用端口

## 📊 环境验证

运行以下命令验证环境配置：

```bash
# 检查Python版本
python --version

# 检查conda环境
conda info

# 检查包安装
python -c "import mitmproxy; print(f'mitmproxy版本: {mitmproxy.__version__}')"

# 测试工具
python test_env.py
```

## 🎉 配置完成

配置完成后，您可以：

1. 使用便捷脚本启动工具
2. 自动管理代理环境变量
3. 监控指定网站的HTTP/HTTPS流量
4. 查看按域名分类的日志文件

享受使用log-proxy工具！
