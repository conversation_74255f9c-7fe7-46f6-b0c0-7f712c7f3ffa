#!/usr/bin/env python3
import os
import sys
import yaml
import logging
import re
import signal
import asyncio
import atexit
import platform
from urllib.parse import urlparse
from mitmproxy import options, http
from mitmproxy.tools import dump
from datetime import datetime

# Windows 特定导入
if platform.system() == 'Windows':
    import winreg
    import ctypes
    from ctypes import wintypes

# 创建logs目录
os.makedirs('logs', exist_ok=True)

# 基础日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

class ProxyEnvironmentManager:
    """管理代理环境变量的类，支持系统级环境变量设置"""

    def __init__(self, listen_port=8080, upstream_host='127.0.0.1', upstream_port=10809):
        self.listen_port = listen_port
        self.upstream_host = upstream_host
        self.upstream_port = upstream_port
        self.original_http_proxy = None
        self.original_https_proxy = None
        self.is_env_set = False
        self.is_windows = platform.system() == 'Windows'

    def _get_system_env_var(self, var_name):
        """获取系统级环境变量"""
        if self.is_windows:
            try:
                with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Environment") as key:
                    value, _ = winreg.QueryValueEx(key, var_name)
                    return value
            except FileNotFoundError:
                return None
            except Exception as e:
                logger.warning(f"读取系统环境变量 {var_name} 失败: {e}")
                return None
        else:
            # Linux/macOS 系统级环境变量通常在 shell 配置文件中
            return os.environ.get(var_name)

    def _set_system_env_var(self, var_name, value):
        """设置系统级环境变量"""
        if self.is_windows:
            try:
                with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Environment", 0, winreg.KEY_SET_VALUE) as key:
                    winreg.SetValueEx(key, var_name, 0, winreg.REG_SZ, value)

                # 通知系统环境变量已更改
                HWND_BROADCAST = 0xFFFF
                WM_SETTINGCHANGE = 0x001A
                SMTO_ABORTIFHUNG = 0x0002
                result = ctypes.c_long()

                ctypes.windll.user32.SendMessageTimeoutW(
                    HWND_BROADCAST,
                    WM_SETTINGCHANGE,
                    0,
                    "Environment",
                    SMTO_ABORTIFHUNG,
                    5000,
                    ctypes.byref(result)
                )
                return True
            except Exception as e:
                logger.error(f"设置系统环境变量 {var_name} 失败: {e}")
                return False
        else:
            # 对于 Linux/macOS，只设置当前进程环境变量
            os.environ[var_name] = value
            logger.warning("Linux/macOS 系统暂不支持系统级环境变量设置，仅设置当前进程环境变量")
            return True

    def _delete_system_env_var(self, var_name):
        """删除系统级环境变量"""
        if self.is_windows:
            try:
                with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Environment", 0, winreg.KEY_SET_VALUE) as key:
                    winreg.DeleteValue(key, var_name)

                # 通知系统环境变量已更改
                HWND_BROADCAST = 0xFFFF
                WM_SETTINGCHANGE = 0x001A
                SMTO_ABORTIFHUNG = 0x0002
                result = ctypes.c_long()

                ctypes.windll.user32.SendMessageTimeoutW(
                    HWND_BROADCAST,
                    WM_SETTINGCHANGE,
                    0,
                    "Environment",
                    SMTO_ABORTIFHUNG,
                    5000,
                    ctypes.byref(result)
                )
                return True
            except FileNotFoundError:
                # 环境变量不存在，这是正常的
                return True
            except Exception as e:
                logger.error(f"删除系统环境变量 {var_name} 失败: {e}")
                return False
        else:
            # 对于 Linux/macOS，只删除当前进程环境变量
            if var_name in os.environ:
                del os.environ[var_name]
            return True

    def set_proxy_environment(self):
        """设置环境变量为当前代理服务器"""
        try:
            # 保存原始环境变量（从系统级获取）
            self.original_http_proxy = self._get_system_env_var('HTTP_PROXY')
            self.original_https_proxy = self._get_system_env_var('HTTPS_PROXY')

            # 设置新的环境变量
            proxy_url = f"http://127.0.0.1:{self.listen_port}"

            # 设置系统级环境变量
            success_http = self._set_system_env_var('HTTP_PROXY', proxy_url)
            success_https = self._set_system_env_var('HTTPS_PROXY', proxy_url)

            # 同时设置当前进程环境变量（立即生效）
            os.environ['HTTP_PROXY'] = proxy_url
            os.environ['HTTPS_PROXY'] = proxy_url

            if success_http and success_https:
                self.is_env_set = True
                if self.is_windows:
                    logger.info(f"已设置系统级代理环境变量: HTTP_PROXY={proxy_url}, HTTPS_PROXY={proxy_url}")
                    logger.info("新启动的程序将自动使用此代理设置")
                else:
                    logger.info(f"已设置当前进程代理环境变量: HTTP_PROXY={proxy_url}, HTTPS_PROXY={proxy_url}")
            else:
                logger.warning("部分环境变量设置失败，可能需要管理员权限")

        except Exception as e:
            logger.error(f"设置代理环境变量失败: {e}")

    def restore_environment(self):
        """恢复原始环境变量"""
        if not self.is_env_set:
            return

        try:
            # 恢复或删除系统级环境变量
            if self.original_http_proxy is not None:
                self._set_system_env_var('HTTP_PROXY', self.original_http_proxy)
                os.environ['HTTP_PROXY'] = self.original_http_proxy
            else:
                self._delete_system_env_var('HTTP_PROXY')
                if 'HTTP_PROXY' in os.environ:
                    del os.environ['HTTP_PROXY']

            if self.original_https_proxy is not None:
                self._set_system_env_var('HTTPS_PROXY', self.original_https_proxy)
                os.environ['HTTPS_PROXY'] = self.original_https_proxy
            else:
                self._delete_system_env_var('HTTPS_PROXY')
                if 'HTTPS_PROXY' in os.environ:
                    del os.environ['HTTPS_PROXY']

            self.is_env_set = False

            if self.original_http_proxy or self.original_https_proxy:
                logger.info("已恢复原始代理环境变量")
            else:
                logger.info("已清除代理环境变量")

        except Exception as e:
            logger.error(f"恢复环境变量失败: {e}")

# 全局环境管理器实例
env_manager = None

def load_config_with_defaults(config_path='config.yaml'):
    """加载配置文件，包含默认值"""
    default_config = {
        'target_urls': [],
        'listen_port': 8080,
        'upstream_host': '127.0.0.1',
        'upstream_port': 10809,
        'upstream_mode': 'http'  # 'http' 或 'socks5'
    }

    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f) or {}
            # 合并默认配置
            for key, value in default_config.items():
                if key not in config:
                    config[key] = value
            return config
    except Exception as e:
        logger.warning(f"加载配置文件失败，使用默认配置: {e}")
        return default_config

class TargetLogger:
    def __init__(self, config_path='config.yaml'):
        config = load_config_with_defaults(config_path)
        self.target_urls = config.get('target_urls', [])
        self.loggers = {}
        # 存储flow_id到请求信息的映射，用于关联请求和响应
        self.flow_requests = {}
        logger.info(f"监控的目标URLs: {self.target_urls}")

    def get_domain_logger(self, url):
        """根据URL获取对应的域名logger"""
        parsed_url = urlparse(url)
        domain = parsed_url.netloc

        # 提取一级域名
        domain_parts = domain.split('.')
        if len(domain_parts) > 2:
            root_domain = '.'.join(domain_parts[-2:])
        else:
            root_domain = domain

        if root_domain not in self.loggers:
            # 为每个域名创建单独的logger
            domain_logger = logging.getLogger(root_domain)
            domain_logger.setLevel(logging.INFO)

            # 创建日志文件名，包含日期
            date_str = datetime.now().strftime("%Y%m%d%H%M")
            log_folder = f"logs/{root_domain}"
            os.makedirs(log_folder, exist_ok=True)  
            log_file = f"{log_folder}/{date_str}.log"

            # 添加文件处理器，使用UTF-8编码
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(logging.Formatter('%(asctime)s [%(levelname)s] %(message)s'))
            domain_logger.addHandler(file_handler)

            # 保存logger
            self.loggers[root_domain] = domain_logger
            logger.info(f"为域名 {root_domain} 创建日志文件: {log_file}")

        return self.loggers[root_domain]

    def should_log_url(self, url):
        """检查URL是否需要被记录"""
        return any(target in url for target in self.target_urls)

    def request(self, flow: http.HTTPFlow) -> None:
        url = flow.request.url
        if self.should_log_url(url):
            domain_logger = self.get_domain_logger(url)

            # 使用flow.id作为唯一标识符
            flow_id = flow.id

            # 记录请求信息，包含flow_id
            domain_logger.info(f"{flow_id} 请求: {flow.request.method} {url}")
            domain_logger.info(f"{flow_id} 请求头: {dict(flow.request.headers)}")

            # 存储请求信息用于后续响应关联
            self.flow_requests[flow_id] = {
                'method': flow.request.method,
                'url': url,
                'timestamp': datetime.now(),
                'domain_logger': domain_logger
            }

            if flow.request.content:
                try:
                    content = flow.request.content.decode('utf-8')
                    domain_logger.info(f"{flow_id} 请求体: {content}")
                except UnicodeDecodeError:
                    domain_logger.info(f"{flow_id} 请求体: [二进制数据，长度: {len(flow.request.content)}字节]")

    def response(self, flow: http.HTTPFlow) -> None:
        MAX_SZ=65536
        url = flow.request.url
        if self.should_log_url(url):
            # 使用flow.id关联请求和响应
            flow_id = flow.id

            # 从存储的请求信息中获取相关数据
            request_info = self.flow_requests.get(flow_id)
            if request_info:
                domain_logger = request_info['domain_logger']

                # 计算响应时间
                response_time = datetime.now()
                duration = (response_time - request_info['timestamp']).total_seconds()

                # 记录响应信息，包含flow_id和响应时间
                if flow.response:
                    domain_logger.info(f"{flow_id} 响应耗时: {duration:.3f}s {flow.response.status_code} {url}")
                    domain_logger.info(f"{flow_id} 响应头: {dict(flow.response.headers)}")

                    if flow.response.content:
                        try:
                            content = flow.response.content.decode('utf-8')
                            # 限制日志大小
                            if len(content) > MAX_SZ:
                                domain_logger.info(f"{flow_id} 响应体 (截断): {content[:MAX_SZ]}...")
                            else:
                                domain_logger.info(f"{flow_id} 响应体: {content}")
                        except UnicodeDecodeError:
                            domain_logger.info(f"{flow_id} 响应体: [二进制数据，长度: {len(flow.response.content)}字节]")
                else:
                    domain_logger.info(f"{flow_id} 响应耗时: {duration:.3f}s 无响应对象")

                # 清理已处理的请求信息，避免内存泄漏
                del self.flow_requests[flow_id]
            else:
                # 如果找不到对应的请求信息，使用备用方案
                domain_logger = self.get_domain_logger(url)
                if flow.response:
                    domain_logger.info(f"{flow_id} 响应未找到对应请求: {flow.response.status_code} {url}")
                else:
                    domain_logger.info(f"{flow_id} 响应未找到对应请求: 无响应对象 {url}")

    def cleanup_stale_requests(self, max_age_seconds=300):
        """清理超过指定时间未收到响应的请求记录，避免内存泄漏"""
        current_time = datetime.now()
        stale_flow_ids = []

        for flow_id, request_info in self.flow_requests.items():
            age = (current_time - request_info['timestamp']).total_seconds()
            if age > max_age_seconds:
                stale_flow_ids.append(flow_id)
                request_info['domain_logger'].warning(
                    f"{flow_id} 请求超时({age:.1f}s)未收到响应: {request_info['method']} {request_info['url']}"
                )

        # 清理过期的请求记录
        for flow_id in stale_flow_ids:
            del self.flow_requests[flow_id]

        if stale_flow_ids:
            logger.info(f"清理了 {len(stale_flow_ids)} 个超时请求记录")

def cleanup_handler(signum=None, frame=None):
    """清理处理器，用于信号处理和程序退出"""
    # 忽略未使用的参数
    _ = signum, frame

    global env_manager, master_instance, cleanup_done

    # 避免重复清理
    if cleanup_done:
        return
    cleanup_done = True

    logger.info("开始清理资源...")
    sys.stdout.flush()  # 强制刷新输出

    if env_manager:
        logger.info("正在清理环境变量...")
        sys.stdout.flush()
        env_manager.restore_environment()

    # 如果有master实例，尝试关闭它
    if 'master_instance' in globals() and master_instance:
        try:
            logger.info("正在关闭mitmproxy...")
            sys.stdout.flush()
            master_instance.shutdown()
        except Exception as e:
            logger.warning(f"关闭master时出错: {e}")
            sys.stdout.flush()

    logger.info("资源清理完成")
    sys.stdout.flush()

# 全局变量
master_instance = None
cleanup_done = False

async def async_start():
    """异步启动函数"""
    global env_manager, master_instance

    # 从配置文件加载设置
    config = load_config_with_defaults()
    listen_port = config.get('listen_port', 8080)
    upstream_host = config.get('upstream_host', '127.0.0.1')
    upstream_port = config.get('upstream_port', 10809)
    upstream_mode = config.get('upstream_mode', 'http')

    # 初始化环境管理器
    env_manager = ProxyEnvironmentManager(listen_port, upstream_host, upstream_port)

    # 注册退出清理函数
    atexit.register(cleanup_handler)

    # 设置代理环境变量
    env_manager.set_proxy_environment()

    # 设置上游代理模式
    if upstream_mode.lower() == 'socks5':
        upstream_url = f"upstream:socks5://{upstream_host}:{upstream_port}"
    else:
        upstream_url = f"upstream:http://{upstream_host}:{upstream_port}"

    # 配置mitmproxy选项
    opts = options.Options(
        listen_host='0.0.0.0',
        listen_port=listen_port,
        mode=[upstream_url],  # mode 需要是一个列表
        upstream_cert=True,
        # HTTPS 拦截配置
        ssl_insecure=True,  # 允许不安全的SSL连接
        confdir='~/.mitmproxy',  # 证书存储目录
        # 自动生成和管理证书
        certs=[],  # 空列表表示自动生成证书
    )

    # 启动mitmproxy
    logger.info(f"启动mitmproxy，监听端口{listen_port}...")
    logger.info(f"上游代理: {upstream_url}")
    logger.info("HTTPS拦截已启用 - 需要安装并信任mitmproxy证书")
    logger.info("证书位置: ~/.mitmproxy/mitmproxy-ca-cert.pem")
    logger.info("Windows证书安装: 双击证书文件 -> 安装到'受信任的根证书颁发机构'")

    master_instance = dump.DumpMaster(opts)
    target_logger = TargetLogger()
    master_instance.addons.add(target_logger)

    # 设置关闭事件
    shutdown_event = asyncio.Event()

    def signal_handler(signum, frame=None):
        """同步信号处理器"""
        _ = frame  # 忽略未使用的参数
        logger.info(f"收到信号 {signum}，正在关闭程序...")
        # 在事件循环中设置关闭事件
        try:
            loop = asyncio.get_running_loop()
            loop.call_soon_threadsafe(shutdown_event.set)
        except RuntimeError:
            # 如果事件循环不可用，直接设置
            shutdown_event.set()

    # 注册信号处理器
    loop = asyncio.get_running_loop()
    for sig in [signal.SIGINT, signal.SIGTERM]:
        try:
            # 尝试使用异步信号处理（Unix/Linux）
            loop.add_signal_handler(sig, lambda s=sig: signal_handler(s))
        except (NotImplementedError, OSError):
            # Windows或其他不支持的系统，使用传统方式
            signal.signal(sig, signal_handler)

    # 创建定期清理任务
    async def periodic_cleanup():
        """定期清理过期的请求记录"""
        while not shutdown_event.is_set():
            try:
                await asyncio.sleep(60)  # 每60秒清理一次
                target_logger.cleanup_stale_requests()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"定期清理任务出错: {e}")

    # 创建运行任务
    run_task = asyncio.create_task(master_instance.run())
    shutdown_task = asyncio.create_task(shutdown_event.wait())
    cleanup_task = asyncio.create_task(periodic_cleanup())

    try:
        # 等待运行任务或关闭信号
        done, pending = await asyncio.wait(
            [run_task, shutdown_task, cleanup_task],
            return_when=asyncio.FIRST_COMPLETED
        )

        # 取消未完成的任务
        for task in pending:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

        # 检查是否是因为关闭信号而退出
        if shutdown_task in done:
            logger.info("收到关闭信号，正在退出...")

    except (KeyboardInterrupt, asyncio.CancelledError):
        logger.info("用户中断，退出程序")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
    finally:
        if master_instance:
            try:
                master_instance.shutdown()
            except Exception as e:
                logger.warning(f"关闭master时出错: {e}")
        cleanup_handler()

def start():
    """同步启动函数，创建事件循环并运行异步函数"""
    try:
        asyncio.run(async_start())
    except KeyboardInterrupt:
        logger.info("用户中断，退出程序")
        cleanup_handler()
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        cleanup_handler()
    finally:
        # 确保清理函数被调用
        cleanup_handler()

if __name__ == "__main__":
    start()