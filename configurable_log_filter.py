#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可配置的日志过滤器 - 基于规则移除已知的冗余模式
"""

import re
import json
from pathlib import Path
from dataclasses import dataclass
from typing import List, Dict, Any, Optional

@dataclass
class FilterRule:
    """过滤规则定义"""
    name: str
    description: str
    pattern: str
    replacement: str
    enabled: bool = True
    preserve_structure: bool = False  # 是否保留结构信息

class ConfigurableLogFilter:
    def __init__(self, config_file: Optional[str] = None):
        """初始化过滤器"""
        self.rules: List[FilterRule] = []
        self.stats = {
            'rules_applied': {},
            'original_size': 0,
            'filtered_size': 0,
            'total_matches': 0
        }

        if config_file and Path(config_file).exists():
            self.load_config(config_file)
        else:
            self.load_default_rules()
            # 保存配置文件供后续使用
            if config_file:
                self.save_config(config_file)
                print(f"\n配置文件已保存到: {config_file}")

    def load_default_rules(self):
        """加载默认的过滤规则"""
        default_rules = [
            FilterRule(
                name="simplify_added_blobs",
                description="简化added_blobs",
                pattern=r'"added_blobs":\[".+?"\]',
                replacement='"added_blobs":"..."',
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="simplify_tool_definitions",
                description="简化tool_definitions",
                pattern=r',"tool_definitions".+?,"nodes"',
                replacement=',"tool_definitions":"...","nodes"',
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="host_header",
                description="Host请求头",
                pattern=r"'host': 'd16\.api\.augmentcode\.com',?\s*",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="connection_header",
                description="Connection请求头",
                pattern=r",?\s*'connection': 'keep-alive'",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="content_json",
                description="Content-Json",
                pattern=r",?\s*'Content-Type': 'application/json'",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="user_agent1",
                description="User-Agent'",
                pattern=r",?\s*'User-Agent':\s*'Augment.*?'",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="user_agent2",
                description='User-Agent"',
                pattern=r',?\s*"user_agent"\s*:\s*"Augment.*?"',
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="accept_all",
                description="简化Accept通配符",
                pattern=r",?\s*'accept': '\*/\*'",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="simplify_accept_language",
                description="简化Accept-Language",
                pattern=r",?\s*'accept-language': '\*'",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="simplify_sec_fetch_mode",
                description="简化sec-fetch-mode",
                pattern=r",?\s*'sec-fetch-mode': 'cors'",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="accept_encoding",
                description="Accept-Encoding",
                pattern=r",?\s*'accept-encoding': 'br, gzip, deflate'",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="simplify_api_base_url",
                description="简化API基础URL",
                pattern=r"https://d16\.api\.augmentcode\.com/",
                replacement="API/",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="shorten_request_id",
                description="缩短请求ID",
                pattern=r"'x-request-id': '([a-f0-9]{8})[a-f0-9-]{28}'",
                replacement=r"'req_id': '\1'",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="shorten_session_id",
                description="缩短会话ID",
                pattern=r"'x-request-session-id': '([a-f0-9]{8})[a-f0-9-]{28}'",
                replacement=r"'sess_id': '\1'",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="auth_token",
                description="缩短认证Token",
                pattern=r",?\s*'Authorization': 'Bearer ([a-f0-9]{8})[a-f0-9]{56}'",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="checkpoint_not_found",
                description="checkpoint_not_found",
                pattern=r',?\s*"checkpoint_not_found":\s*false',
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="shorten_blob_name",
                description="缩短blob名称",
                pattern=r'"blob_name":"([a-f0-9]{8})[a-f0-9]{56}"',
                replacement=r'"blob":"\1"',
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="remove_milliseconds",
                description="移除时间戳毫秒部分",
                pattern=r"(\d{2}:\d{2}:\d{2}),\d{3}",
                replacement=r"\1",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="content_length",
                description="Content-Length",
                pattern=r",?\s*'Content-Length': '\d+'",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="response_content_type",
                description="响应Content-Type",
                pattern=r",?\s*'content-type': 'application/json'",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="date_header",
                description="简化Date头",
                pattern=r",?\s*'date': '[^']*'",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="via_header",
                description="Via头",
                pattern=r",?\s*'Via': '1\.1 google'",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="alt_svc",
                description="简化Alt-Svc头",
                pattern=r",?\s*'Alt-Svc': '[^']*'",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="api_version",
                description="移除API版本字段",
                pattern=r",?\s*'x-api-version': '[^']*'",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="transfer_encoding",
                description="移除Transfer-Encoding字段",
                pattern=r",?\s*'Transfer-Encoding': 'chunked'",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="remove_empty",
                description="移除null/[]/{}/''/\"\" key-value",
                pattern=r',?\s*"\w+"\s*:\s*(null|\[\s*\]|\{\s*\}|"\s*"|\'s*\')|"unknown"',
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="remove_time",
                description="移除不关心的time",
                pattern=r',?\s*"\w*time\w*"\s*:\s*\d+',
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="subscription-info",
                description="订阅校验",
                pattern=r"\d+:\d+:\d+ .+: POST API/subscription-info(\n.+?)+?false\}\}\}\n*",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="empty-header-response",
                description="空请求头/体",
                pattern=r"\d+-\d+-\d+ \d+:\d+:\d+ .+?: \{\}\n",
                replacement="",
                enabled=True,
                preserve_structure=False
            ),
            FilterRule(
                name="count_json_objects",
                description="统计JSON对象数量",
                pattern=r"\{[^{}]*\}",
                replacement="",
                enabled=False,
                preserve_structure=True
            )
        ]

        self.rules = default_rules

    def load_config(self, config_file: str):
        """从配置文件加载规则"""
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)

        self.rules = []
        for rule_data in config.get('rules', []):
            rule = FilterRule(**rule_data)
            self.rules.append(rule)

    def save_config(self, config_file: str):
        """保存配置到文件"""
        config = {
            'rules': [
                {
                    'name': rule.name,
                    'description': rule.description,
                    'pattern': rule.pattern,
                    'replacement': rule.replacement,
                    'enabled': rule.enabled,
                    'preserve_structure': rule.preserve_structure
                }
                for rule in self.rules
            ]
        }

        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

    def apply_filters(self, text: str, dry_run: bool = False) -> str:
        """应用过滤规则"""
        self.stats['original_size'] = len(text.encode('utf-8'))
        self.stats['rules_applied'] = {}
        self.stats['total_matches'] = 0

        filtered_text = text

        for rule in self.rules:
            if not rule.enabled:
                continue

            # 统计匹配数量
            matches = re.findall(rule.pattern, filtered_text)
            match_count = len(matches)

            self.stats['rules_applied'][rule.name] = {
                'description': rule.description,
                'matches': match_count,
                'applied': not dry_run and not rule.preserve_structure
            }
            self.stats['total_matches'] += match_count

            # 如果不是dry_run且不是preserve_structure，则应用替换
            if not dry_run and not rule.preserve_structure:
                filtered_text = re.sub(rule.pattern, rule.replacement, filtered_text)

        self.stats['filtered_size'] = len(filtered_text.encode('utf-8'))
        return filtered_text

    def get_compression_stats(self) -> Dict[str, Any]:
        """获取压缩统计信息"""
        if self.stats['original_size'] > 0:
            compression_ratio = (1 - self.stats['filtered_size'] / self.stats['original_size']) * 100
        else:
            compression_ratio = 0

        return {
            'original_size_bytes': self.stats['original_size'],
            'filtered_size_bytes': self.stats['filtered_size'],
            'compression_ratio_percent': compression_ratio,
            'total_matches': self.stats['total_matches'],
            'rules_applied': self.stats['rules_applied']
        }

    def print_stats(self):
        """打印统计信息"""
        stats = self.get_compression_stats()

        print("=" * 60)
        print("日志过滤统计报告")
        print("=" * 60)
        print(f"原始大小: {stats['original_size_bytes']:,} 字节")
        print(f"过滤后大小: {stats['filtered_size_bytes']:,} 字节")
        print(f"压缩率: {stats['compression_ratio_percent']:.1f}%")
        print(f"总匹配数: {stats['total_matches']:,}")
        print()

        print("规则应用详情:")
        print("-" * 60)
        for rule_name, rule_stats in stats['rules_applied'].items():
            status = "✓ 已应用" if rule_stats['applied'] else "○ 仅统计"
            print(f"{status} {rule_stats['description']}: {rule_stats['matches']:,} 次")

def filter_log_file(input_file: str, output_file: str, config_file: Optional[str] = None, dry_run: bool = False):
    """过滤日志文件"""
    print(f"开始处理日志文件: {input_file}")

    # 读取输入文件
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 创建过滤器并应用规则
    filter_obj = ConfigurableLogFilter(config_file)
    filtered_content = filter_obj.apply_filters(content, dry_run=dry_run)

    # 保存结果
    if not dry_run:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(filtered_content)
        print(f"过滤结果已保存到: {output_file}")
    else:
        print("干运行模式 - 未保存文件")

    # 打印统计信息
    filter_obj.print_stats()

    return filter_obj

if __name__ == "__main__":
    input_file = "logs/augmentcode.com_20250525.log"
    output_file = "logs/augmentcode.com_20250525.filtered.log"
    config_file = "logs/filter_config.json"

    if Path(input_file).exists():
        # 先进行干运行，查看效果
        # print("=== 干运行模式 ===")
        # filter_obj = filter_log_file(input_file, output_file, config_file, dry_run=True)

        # 询问是否执行实际过滤
        print("\n=== 实际过滤 ===")
        filter_log_file(input_file, output_file, config_file, dry_run=False)
    else:
        print(f"文件 {input_file} 不存在")
