#!/bin/bash
# 使用conda base环境运行log-proxy工具
# 这个脚本确保使用conda base环境中的Python和依赖包

echo "正在使用conda base环境启动log-proxy工具..."
echo

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查conda是否可用
if ! command -v conda &> /dev/null; then
    echo "错误: 找不到conda命令，请确保conda已正确安装并添加到PATH"
    exit 1
fi

# 获取conda base环境路径
CONDA_BASE=$(conda info --base)
CONDA_PYTHON="$CONDA_BASE/bin/python"

# 在Windows上，Python可执行文件路径不同
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    CONDA_PYTHON="$CONDA_BASE/python.exe"
fi

# 检查conda base环境的Python是否存在
if [[ ! -f "$CONDA_PYTHON" ]]; then
    echo "错误: 找不到conda base环境的Python: $CONDA_PYTHON"
    exit 1
fi

echo "使用Python: $CONDA_PYTHON"
echo

# 使用conda base环境的Python运行主程序
"$CONDA_PYTHON" main.py

echo
echo "程序已退出"
