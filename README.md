# HTTP/HTTPS 代理调试工具

这是一个基于 Mitmproxy 的调试工具，用于记录特定 URL 的 HTTP 和 HTTPS 通信。

## 功能特点

- 监听指定端口处理 HTTP/HTTPS 请求（默认 8080）
- 支持上游代理（HTTP 或 SOCKS5）
- 根据一级域名自动分类创建日志文件
- 可通过配置文件指定要监控的目标 URL
- **自动环境变量管理**：启动时设置全局代理环境变量，关闭时恢复为上游代理
- 支持信号处理，确保程序异常退出时也能正确清理环境变量

## 使用方法

### 方法一：使用conda base环境（推荐）

1. 检查和设置conda环境：
   ```bash
   python setup_conda_env.py
   ```

2. 使用conda环境运行：
   ```bash
   # Windows
   run_with_conda.bat

   # Linux/macOS
   ./run_with_conda.sh
   ```

### 方法二：手动安装依赖

1. 安装依赖：
   ```bash
   pip install mitmproxy pyyaml
   ```

### 通用配置步骤

1. 配置设置：
   编辑 `config.yaml` 文件，配置要监控的 URL 和代理设置：
   ```yaml
   # 配置要监控的目标URLs
   target_urls:
     - "example.com"
     - "api.example.org"
     - "github.com"

   # 代理服务器配置
   listen_port: 8080          # 本地监听端口
   upstream_host: "127.0.0.1" # 上游代理主机
   upstream_port: 10809       # 上游代理端口
   upstream_mode: "http"      # 上游代理模式: "http" 或 "socks5"
   ```

2. 运行脚本：
   ```bash
   python main.py
   ```

3. 环境变量自动管理：
   - 程序启动时会自动设置 `HTTP_PROXY` 和 `HTTPS_PROXY` 环境变量为本地监听地址
   - 程序关闭时会自动恢复环境变量为上游代理地址
   - 支持 Ctrl+C 中断和系统信号处理

4. 查看日志：
   日志文件将保存在 `logs` 目录下，按域名和日期命名。

## 测试功能

运行测试脚本验证环境变量管理功能：
```
python test_env.py
```

## 注意事项

- 您需要自行安装和配置 Mitmproxy 的 CA 证书，以便能够解密 HTTPS 流量。
- 程序会自动管理系统环境变量，确保其他应用程序能够使用正确的代理设置。
- 支持 HTTP 和 SOCKS5 两种上游代理模式，可在配置文件中切换。