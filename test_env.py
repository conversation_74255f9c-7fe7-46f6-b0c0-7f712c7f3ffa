#!/usr/bin/env python3
"""
测试环境变量管理功能的脚本
"""
import os
import time
from main import ProxyEnvironmentManager

def test_environment_manager():
    """测试环境变量管理器"""
    print("=== 测试环境变量管理功能 ===")
    
    # 显示初始环境变量
    print(f"初始 HTTP_PROXY: {os.environ.get('HTTP_PROXY', '未设置')}")
    print(f"初始 HTTPS_PROXY: {os.environ.get('HTTPS_PROXY', '未设置')}")
    
    # 创建环境管理器
    env_manager = ProxyEnvironmentManager(listen_port=8080, upstream_host='127.0.0.1', upstream_port=10809)
    
    # 设置代理环境变量
    print("\n--- 设置代理环境变量 ---")
    env_manager.set_proxy_environment()
    print(f"设置后 HTTP_PROXY: {os.environ.get('HTTP_PROXY', '未设置')}")
    print(f"设置后 HTTPS_PROXY: {os.environ.get('HTTPS_PROXY', '未设置')}")
    
    # 等待一下
    time.sleep(2)
    
    # 恢复环境变量
    print("\n--- 恢复环境变量 ---")
    env_manager.restore_environment()
    print(f"恢复后 HTTP_PROXY: {os.environ.get('HTTP_PROXY', '未设置')}")
    print(f"恢复后 HTTPS_PROXY: {os.environ.get('HTTPS_PROXY', '未设置')}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_environment_manager()
