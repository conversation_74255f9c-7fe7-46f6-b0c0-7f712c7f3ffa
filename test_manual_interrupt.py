#!/usr/bin/env python3
"""
手动测试Ctrl+C中断的脚本
"""
import os
import sys

def main():
    """主函数"""
    print("🚀 手动中断测试")
    print("=" * 40)
    print("这个测试将启动log-proxy程序")
    print("请在程序启动后按 Ctrl+C 来测试中断处理")
    print("观察程序是否能正确显示清理信息并退出")
    print()
    
    # 显示当前环境变量
    print("测试前的环境变量:")
    print(f"HTTP_PROXY: {os.environ.get('HTTP_PROXY', '未设置')}")
    print(f"HTTPS_PROXY: {os.environ.get('HTTPS_PROXY', '未设置')}")
    print()
    
    input("按回车键开始测试...")
    print()
    
    try:
        # 直接导入并运行主程序
        from main import start
        start()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
    
    print("\n测试后的环境变量:")
    print(f"HTTP_PROXY: {os.environ.get('HTTP_PROXY', '未设置')}")
    print(f"HTTPS_PROXY: {os.environ.get('HTTPS_PROXY', '未设置')}")
    print("\n测试完成！")

if __name__ == "__main__":
    main()
