#!/usr/bin/env python3
"""
检查和设置conda base环境的脚本
确保所需的依赖包已正确安装
"""
import subprocess
import sys
import os

def run_command(cmd, check=True):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, check=check)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr

def check_conda():
    """检查conda是否可用"""
    print("🔍 检查conda环境...")
    success, stdout, stderr = run_command("conda --version", check=False)
    if success:
        print(f"✅ conda已安装: {stdout.strip()}")
        return True
    else:
        print("❌ conda未找到，请先安装Miniconda或Anaconda")
        return False

def get_conda_info():
    """获取conda环境信息"""
    print("\n📋 获取conda环境信息...")
    success, stdout, stderr = run_command("conda info --json", check=False)
    if success:
        import json
        info = json.loads(stdout)
        base_env = info.get('default_prefix', '')
        python_version = info.get('python_version', '')
        print(f"✅ Base环境路径: {base_env}")
        print(f"✅ Python版本: {python_version}")
        return base_env
    else:
        print("❌ 无法获取conda信息")
        return None

def check_python_packages():
    """检查Python包是否已安装"""
    print("\n📦 检查Python包...")
    required_packages = ['yaml', 'mitmproxy']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'yaml':
                import yaml
                print(f"✅ {package} 已安装")
            elif package == 'mitmproxy':
                import mitmproxy
                print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    return missing_packages

def install_missing_packages(missing_packages):
    """安装缺失的包"""
    if not missing_packages:
        return True
    
    print(f"\n📥 安装缺失的包: {', '.join(missing_packages)}")
    
    # 映射包名到pip安装名
    pip_names = {
        'yaml': 'pyyaml',
        'mitmproxy': 'mitmproxy'
    }
    
    for package in missing_packages:
        pip_name = pip_names.get(package, package)
        print(f"正在安装 {pip_name}...")
        success, stdout, stderr = run_command(f"pip install {pip_name}", check=False)
        if success:
            print(f"✅ {pip_name} 安装成功")
        else:
            print(f"❌ {pip_name} 安装失败: {stderr}")
            return False
    
    return True

def test_tool():
    """测试工具是否能正常运行"""
    print("\n🧪 测试工具...")
    if os.path.exists('test_env.py'):
        success, stdout, stderr = run_command("python test_env.py", check=False)
        if success:
            print("✅ 工具测试通过")
            return True
        else:
            print(f"❌ 工具测试失败: {stderr}")
            return False
    else:
        print("⚠️  测试文件不存在，跳过测试")
        return True

def main():
    """主函数"""
    print("🚀 log-proxy工具conda环境设置")
    print("=" * 50)
    
    # 检查conda
    if not check_conda():
        sys.exit(1)
    
    # 获取conda信息
    base_env = get_conda_info()
    if not base_env:
        sys.exit(1)
    
    # 检查Python包
    missing_packages = check_python_packages()
    
    # 安装缺失的包
    if missing_packages:
        if not install_missing_packages(missing_packages):
            sys.exit(1)
        
        # 重新检查
        print("\n🔄 重新检查包安装状态...")
        missing_packages = check_python_packages()
        if missing_packages:
            print(f"❌ 仍有包未安装: {', '.join(missing_packages)}")
            sys.exit(1)
    
    # 测试工具
    if not test_tool():
        sys.exit(1)
    
    print("\n🎉 conda环境设置完成！")
    print("\n📝 使用方法:")
    print("  Windows: 双击 run_with_conda.bat")
    print("  Linux/macOS: ./run_with_conda.sh")
    print("  或直接运行: python main.py")

if __name__ == "__main__":
    main()
