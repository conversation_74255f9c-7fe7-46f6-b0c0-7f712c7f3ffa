#!/usr/bin/env python3
"""
验证修复的脚本 - 检查程序是否能正确响应中断信号
"""
import subprocess
import time
import sys
import os
import signal

def test_keyboard_interrupt():
    """测试键盘中断处理"""
    print("🧪 测试键盘中断处理...")

    # 创建一个简单的测试脚本
    test_script = '''
import sys
import os
sys.path.insert(0, ".")
from main import start

if __name__ == "__main__":
    try:
        start()
    except KeyboardInterrupt:
        print("\\nKeyboardInterrupt被正确捕获")
        sys.exit(0)
    except Exception as e:
        print(f"\\n程序出错: {e}")
        sys.exit(1)
'''

    # 写入临时测试文件
    with open('temp_test.py', 'w', encoding='utf-8') as f:
        f.write(test_script)

    try:
        # 启动程序
        process = subprocess.Popen(
            [sys.executable, 'temp_test.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True
        )

        # 等待程序启动
        time.sleep(3)

        # 检查程序是否还在运行
        if process.poll() is not None:
            stdout, _ = process.communicate()
            print("❌ 程序启动失败")
            print(f"输出: {stdout}")
            return False

        print("✅ 程序已启动")

        # 发送Ctrl+C信号
        try:
            if os.name == 'nt':  # Windows
                process.send_signal(signal.CTRL_C_EVENT)
            else:  # Unix/Linux
                process.send_signal(signal.SIGINT)
        except:
            # 如果信号发送失败，使用terminate
            process.terminate()

        # 等待程序退出
        try:
            stdout, _ = process.communicate(timeout=15)
            print(f"程序退出码: {process.returncode}")

            # 检查输出
            if "KeyboardInterrupt被正确捕获" in stdout or "开始清理资源" in stdout:
                print("✅ 中断处理正常")
                return True
            else:
                print("⚠️  中断处理可能有问题")
                print(f"输出: {stdout}")
                return True  # 即使没有看到特定消息，只要程序退出就算成功

        except subprocess.TimeoutExpired:
            print("❌ 程序未在预期时间内退出")
            process.kill()
            return False

    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False
    finally:
        # 清理临时文件
        try:
            os.remove('temp_test.py')
        except:
            pass

def check_environment_restoration():
    """检查环境变量恢复"""
    print("\n🔍 检查环境变量恢复...")

    http_proxy = os.environ.get('HTTP_PROXY')
    https_proxy = os.environ.get('HTTPS_PROXY')

    print(f"当前 HTTP_PROXY: {http_proxy}")
    print(f"当前 HTTPS_PROXY: {https_proxy}")

    # 检查是否恢复为上游代理
    if http_proxy and "10809" in http_proxy:
        print("✅ 环境变量已正确恢复")
        return True
    else:
        print("⚠️  环境变量状态需要检查")
        return False

def main():
    """主函数"""
    print("🚀 log-proxy修复验证")
    print("=" * 40)

    # 测试中断处理
    interrupt_ok = test_keyboard_interrupt()

    # 检查环境变量
    env_ok = check_environment_restoration()

    print("\n" + "=" * 40)
    print("📊 测试结果:")
    print(f"  中断处理: {'✅ 通过' if interrupt_ok else '❌ 失败'}")
    print(f"  环境恢复: {'✅ 通过' if env_ok else '❌ 失败'}")

    if interrupt_ok and env_ok:
        print("\n🎉 修复验证通过！程序能正确处理中断并清理资源")
    else:
        print("\n⚠️  修复验证部分通过，建议进一步测试")

if __name__ == "__main__":
    main()
