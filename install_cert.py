#!/usr/bin/env python3
"""
mitmproxy证书安装助手
"""
import os
import sys
import platform
import subprocess
import shutil
from pathlib import Path

def get_mitmproxy_cert_path():
    """获取mitmproxy证书路径"""
    home = Path.home()
    cert_dir = home / '.mitmproxy'
    cert_file = cert_dir / 'mitmproxy-ca-cert.pem'
    return cert_dir, cert_file

def check_cert_exists():
    """检查证书是否存在"""
    cert_dir, cert_file = get_mitmproxy_cert_path()
    return cert_file.exists(), cert_dir, cert_file

def install_cert_windows(cert_file):
    """在Windows上安装证书"""
    try:
        # 使用certlm.msc或certutil命令安装证书
        print("正在安装证书到Windows证书存储...")
        
        # 方法1: 使用certutil命令
        result = subprocess.run([
            'certutil', '-addstore', '-user', 'Root', str(cert_file)
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 证书安装成功!")
            return True
        else:
            print(f"❌ certutil安装失败: {result.stderr}")
            
            # 方法2: 提示用户手动安装
            print("\n请手动安装证书:")
            print(f"1. 双击证书文件: {cert_file}")
            print("2. 点击'安装证书'")
            print("3. 选择'当前用户'")
            print("4. 选择'将所有的证书都放入下列存储'")
            print("5. 点击'浏览' -> 选择'受信任的根证书颁发机构'")
            print("6. 点击'确定'完成安装")
            
            # 打开证书文件
            try:
                os.startfile(str(cert_file))
                print(f"\n已打开证书文件: {cert_file}")
            except Exception as e:
                print(f"无法打开证书文件: {e}")
            
            return False
            
    except Exception as e:
        print(f"❌ 安装证书时出错: {e}")
        return False

def install_cert_macos(cert_file):
    """在macOS上安装证书"""
    try:
        print("正在安装证书到macOS钥匙串...")
        result = subprocess.run([
            'security', 'add-trusted-cert', '-d', '-r', 'trustRoot', 
            '-k', '/Library/Keychains/System.keychain', str(cert_file)
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 证书安装成功!")
            return True
        else:
            print(f"❌ 证书安装失败: {result.stderr}")
            print("\n请手动安装证书:")
            print(f"1. 双击证书文件: {cert_file}")
            print("2. 在钥匙串访问中找到mitmproxy证书")
            print("3. 双击证书 -> 信任 -> 设置为'始终信任'")
            return False
            
    except Exception as e:
        print(f"❌ 安装证书时出错: {e}")
        return False

def install_cert_linux(cert_file):
    """在Linux上安装证书"""
    try:
        print("正在安装证书到Linux系统...")
        
        # 复制证书到系统证书目录
        cert_dest = Path('/usr/local/share/ca-certificates/mitmproxy-ca-cert.crt')
        
        # 需要sudo权限
        result = subprocess.run([
            'sudo', 'cp', str(cert_file), str(cert_dest)
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            # 更新证书存储
            update_result = subprocess.run([
                'sudo', 'update-ca-certificates'
            ], capture_output=True, text=True)
            
            if update_result.returncode == 0:
                print("✅ 证书安装成功!")
                return True
            else:
                print(f"❌ 更新证书存储失败: {update_result.stderr}")
        else:
            print(f"❌ 复制证书失败: {result.stderr}")
            
        print("\n请手动安装证书:")
        print(f"1. sudo cp {cert_file} /usr/local/share/ca-certificates/mitmproxy-ca-cert.crt")
        print("2. sudo update-ca-certificates")
        return False
        
    except Exception as e:
        print(f"❌ 安装证书时出错: {e}")
        return False

def generate_cert():
    """生成mitmproxy证书"""
    print("正在生成mitmproxy证书...")
    try:
        # 启动mitmproxy一次以生成证书
        result = subprocess.run([
            sys.executable, '-c', 
            'from mitmproxy import options, master; '
            'opts = options.Options(); '
            'm = master.Master(opts); '
            'm.shutdown()'
        ], capture_output=True, text=True, timeout=10)
        
        cert_exists, cert_dir, cert_file = check_cert_exists()
        if cert_exists:
            print(f"✅ 证书生成成功: {cert_file}")
            return True
        else:
            print("❌ 证书生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 生成证书时出错: {e}")
        return False

def main():
    print("=== mitmproxy证书安装助手 ===")
    print(f"操作系统: {platform.system()}")
    print()
    
    # 检查证书是否存在
    cert_exists, cert_dir, cert_file = check_cert_exists()
    
    if not cert_exists:
        print(f"❌ 证书不存在: {cert_file}")
        print("请先启动log-proxy工具生成证书，或手动生成证书")
        
        choice = input("是否现在生成证书? (y/n): ").lower().strip()
        if choice == 'y':
            if generate_cert():
                cert_exists = True
            else:
                print("证书生成失败，退出")
                return
        else:
            return
    
    print(f"✅ 找到证书: {cert_file}")
    print()
    
    # 根据操作系统安装证书
    system = platform.system()
    
    if system == 'Windows':
        success = install_cert_windows(cert_file)
    elif system == 'Darwin':  # macOS
        success = install_cert_macos(cert_file)
    elif system == 'Linux':
        success = install_cert_linux(cert_file)
    else:
        print(f"❌ 不支持的操作系统: {system}")
        success = False
    
    if success:
        print("\n🎉 证书安装完成!")
        print("现在可以拦截HTTPS流量了")
    else:
        print("\n⚠️  请按照提示手动安装证书")
    
    print(f"\n证书文件位置: {cert_file}")

if __name__ == "__main__":
    main()
