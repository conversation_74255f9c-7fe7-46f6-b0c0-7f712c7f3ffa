#!/usr/bin/env python3
"""
测试程序优雅关闭的脚本
"""
import subprocess
import time
import sys
import os

def test_graceful_shutdown():
    """测试程序的优雅关闭"""
    print("🧪 测试程序优雅关闭...")
    
    # 启动主程序
    print("启动主程序...")
    
    # 使用Popen启动程序，这样可以更好地控制
    cmd = [sys.executable, "main.py"]
    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True,
        creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
    )
    
    try:
        # 等待程序启动
        print("等待程序启动...")
        time.sleep(3)
        
        # 检查程序是否正在运行
        if process.poll() is not None:
            print("❌ 程序启动失败")
            stdout, _ = process.communicate()
            print(f"输出: {stdout}")
            return False
        
        print("✅ 程序已启动")
        
        # 终止程序
        print("终止程序...")
        process.terminate()
        
        # 等待程序退出
        print("等待程序退出...")
        try:
            stdout, _ = process.communicate(timeout=10)
            print(f"程序退出码: {process.returncode}")
            
            # 检查输出中是否包含清理信息
            if "正在清理环境变量" in stdout or "已恢复代理环境变量" in stdout:
                print("✅ 检测到环境变量清理")
                return True
            else:
                print("⚠️  未检测到环境变量清理信息")
                print(f"程序输出:\n{stdout}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ 程序未在预期时间内退出，强制终止")
            process.kill()
            stdout, _ = process.communicate()
            print(f"强制终止后的输出:\n{stdout}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        if process.poll() is None:
            process.kill()
        return False

def check_env_vars():
    """检查环境变量是否正确恢复"""
    print("\n🔍 检查环境变量...")
    http_proxy = os.environ.get('HTTP_PROXY')
    https_proxy = os.environ.get('HTTPS_PROXY')
    
    print(f"HTTP_PROXY: {http_proxy}")
    print(f"HTTPS_PROXY: {https_proxy}")
    
    # 检查是否恢复为上游代理
    if http_proxy and "10809" in http_proxy:
        print("✅ HTTP_PROXY已恢复为上游代理")
        return True
    else:
        print("⚠️  HTTP_PROXY未正确恢复")
        return False

def main():
    """主函数"""
    print("🚀 log-proxy优雅关闭测试")
    print("=" * 40)
    
    success = test_graceful_shutdown()
    env_ok = check_env_vars()
    
    print("\n" + "=" * 40)
    if success and env_ok:
        print("🎉 优雅关闭测试通过！")
    else:
        print("❌ 优雅关闭测试失败！")
        if not success:
            print("  - 程序未正确关闭")
        if not env_ok:
            print("  - 环境变量未正确恢复")

if __name__ == "__main__":
    main()
