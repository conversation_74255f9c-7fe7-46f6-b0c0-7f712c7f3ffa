#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能压缩模拟器 - 基于缓存的字符串去重压缩
"""

import re
import json
from collections import OrderedDict
from pathlib import Path

class IntelligentCompressor:
    def __init__(self, max_cache_size=2048, max_cache_memory=4*1024*1024):
        """
        初始化智能压缩器

        Args:
            max_cache_size: 最大缓存条目数
            max_cache_memory: 最大缓存内存大小(字节)
        """
        self.cache = OrderedDict()  # 使用OrderedDict实现LRU
        self.max_cache_size = max_cache_size
        self.max_cache_memory = max_cache_memory
        self.current_memory = 0
        self.next_id = 1

        # 统计信息
        self.stats = {
            'total_strings': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'original_bytes': 0,
            'compressed_bytes': 0
        }

    def _get_cache_key(self, string_content):
        """生成缓存键"""
        return string_content

    def _generate_short_name(self):
        """生成短名字 (hex序列号)"""
        short_name = f"#{self.next_id:04x}"
        self.next_id += 1
        return short_name

    def _evict_if_needed(self, new_string_size):
        """如果需要，清理缓存"""
        # 检查内存限制
        while (self.current_memory + new_string_size > self.max_cache_memory and
               len(self.cache) > 0):
            # 移除最旧的条目 (LRU)
            oldest_key, oldest_value = self.cache.popitem(last=False)
            self.current_memory -= len(oldest_key)

        # 检查条目数限制
        while len(self.cache) >= self.max_cache_size:
            oldest_key, oldest_value = self.cache.popitem(last=False)
            self.current_memory -= len(oldest_key)

    def compress_string(self, string_content):
        """压缩单个字符串"""
        self.stats['total_strings'] += 1
        self.stats['original_bytes'] += len(string_content)

        cache_key = self._get_cache_key(string_content)

        if cache_key in self.cache:
            # 缓存命中
            self.stats['cache_hits'] += 1
            short_name = self.cache[cache_key]
            # 移动到末尾 (LRU更新)
            self.cache.move_to_end(cache_key)
            self.stats['compressed_bytes'] += len(short_name)
            return short_name
        else:
            # 缓存未命中
            self.stats['cache_misses'] += 1

            # 检查是否需要清理缓存
            self._evict_if_needed(len(cache_key))

            # 生成短名字并加入缓存
            short_name = self._generate_short_name()
            self.cache[cache_key] = short_name
            self.current_memory += len(cache_key)

            self.stats['compressed_bytes'] += len(short_name)
            return short_name

    def compress_text(self, text):
        """压缩文本中的所有字符串字面量"""
        # 定义更精确的正则模式，避免匹配单个字符
        patterns = [
            r"'[^']{2,}'",  # 单引号字符串，至少3个字符（包括引号）
            r'"[^"]{2,}"'   # 双引号字符串，至少3个字符（包括引号）
        ]

        compressed_text = text

        for pattern in patterns:
            def replace_func(match):
                original = match.group(0)
                # 只压缩长度超过5的字符串
                if len(original) > 5:
                    compressed = self.compress_string(original)
                    return compressed
                return original

            compressed_text = re.sub(pattern, replace_func, compressed_text)

        return compressed_text

    def get_cache_dict(self):
        """获取缓存字典用于解码"""
        return {v: k for k, v in self.cache.items()}

    def get_stats(self):
        """获取压缩统计信息"""
        if self.stats['original_bytes'] > 0:
            compression_ratio = (1 - self.stats['compressed_bytes'] / self.stats['original_bytes']) * 100
        else:
            compression_ratio = 0

        return {
            **self.stats,
            'compression_ratio': compression_ratio,
            'cache_size': len(self.cache),
            'cache_memory': self.current_memory,
            'hit_rate': (self.stats['cache_hits'] / max(1, self.stats['total_strings'])) * 100
        }

def simulate_compression(input_file, output_file, cache_file):
    """模拟智能压缩过程"""
    print("开始智能压缩模拟...")

    # 读取原始文件
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 创建压缩器
    compressor = IntelligentCompressor()

    # 逐行处理以便观察效果
    lines = content.split('\n')
    compressed_lines = []

    for i, line in enumerate(lines):
        compressed_line = compressor.compress_text(line)
        compressed_lines.append(compressed_line)

        # 每100行输出一次进度
        if (i + 1) % 100 == 0:
            stats = compressor.get_stats()
            print(f"处理了 {i+1} 行, 缓存命中率: {stats['hit_rate']:.1f}%, "
                  f"压缩率: {stats['compression_ratio']:.1f}%")

    # 保存压缩后的文件
    compressed_content = '\n'.join(compressed_lines)
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(compressed_content)

    # 保存缓存字典
    cache_dict = compressor.get_cache_dict()
    with open(cache_file, 'w', encoding='utf-8') as f:
        json.dump(cache_dict, f, ensure_ascii=False, indent=2)

    # 输出最终统计
    stats = compressor.get_stats()
    print("\n" + "="*50)
    print("智能压缩完成!")
    print(f"原始大小: {stats['original_bytes']} 字节")
    print(f"压缩后大小: {stats['compressed_bytes']} 字节")
    print(f"压缩率: {stats['compression_ratio']:.1f}%")
    print(f"缓存条目数: {stats['cache_size']}")
    print(f"缓存内存使用: {stats['cache_memory']} 字节")
    print(f"缓存命中率: {stats['hit_rate']:.1f}%")
    print(f"总字符串数: {stats['total_strings']}")
    print(f"缓存命中: {stats['cache_hits']}")
    print(f"缓存未命中: {stats['cache_misses']}")

    return stats

def decompress_file(compressed_file, cache_file, output_file):
    """解压缩文件"""
    print("\n开始解压缩...")

    # 读取缓存字典
    with open(cache_file, 'r', encoding='utf-8') as f:
        cache_dict = json.load(f)

    # 读取压缩文件
    with open(compressed_file, 'r', encoding='utf-8') as f:
        compressed_content = f.read()

    # 解压缩 - 按长度排序，先替换长的，避免部分匹配问题
    decompressed_content = compressed_content
    sorted_items = sorted(cache_dict.items(), key=lambda x: len(x[0]), reverse=True)

    for short_name, original in sorted_items:
        decompressed_content = decompressed_content.replace(short_name, original)

    # 保存解压缩文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(decompressed_content)

    print(f"解压缩完成，输出到: {output_file}")

if __name__ == "__main__":
    input_file = "logs/3399157.small.log"
    compressed_file = "logs/3399157.intelligent.log"
    cache_file = "logs/3399157.cache.json"
    decompressed_file = "logs/3399157.decompressed.log"

    if Path(input_file).exists():
        # 执行压缩
        stats = simulate_compression(input_file, compressed_file, cache_file)

        # 执行解压缩验证
        decompress_file(compressed_file, cache_file, decompressed_file)

        # 验证完整性
        with open(input_file, 'r', encoding='utf-8') as f:
            original = f.read()
        with open(decompressed_file, 'r', encoding='utf-8') as f:
            decompressed = f.read()

        if original == decompressed:
            print("✅ 完整性验证通过！解压缩结果与原文件完全一致")
        else:
            print("❌ 完整性验证失败！解压缩结果与原文件不一致")
    else:
        print(f"文件 {input_file} 不存在")
