{"rules": [{"name": "simplify_added_blobs", "description": "简化added_blobs", "pattern": "\"added_blobs\":\\[\".+?\"\\]", "replacement": "\"added_blobs\":\"...\"", "enabled": true, "preserve_structure": false}, {"name": "simplify_tool_definitions", "description": "简化tool_definitions", "pattern": ",\"tool_definitions\".+?,\"nodes\"", "replacement": ",\"tool_definitions\":\"...\",\"nodes\"", "enabled": true, "preserve_structure": false}, {"name": "host_header", "description": "Host请求头", "pattern": "'host': 'd16\\.api\\.augmentcode\\.com',?\\s*", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "connection_header", "description": "Connection请求头", "pattern": ",?\\s*'connection': 'keep-alive'", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "content_json", "description": "Content-<PERSON><PERSON>", "pattern": ",?\\s*'Content-Type': 'application/json'", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "user_agent1", "description": "User-Agent'", "pattern": ",?\\s*'User-Agent':\\s*'Augment.*?'", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "user_agent2", "description": "User-Agent\"", "pattern": ",?\\s*\"user_agent\"\\s*:\\s*\"Augment.*?\"", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "accept_all", "description": "简化Accept通配符", "pattern": ",?\\s*'accept': '\\*/\\*'", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "simplify_accept_language", "description": "简化Accept-Language", "pattern": ",?\\s*'accept-language': '\\*'", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "simplify_sec_fetch_mode", "description": "简化sec-fetch-mode", "pattern": ",?\\s*'sec-fetch-mode': 'cors'", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "accept_encoding", "description": "Accept-Encoding", "pattern": ",?\\s*'accept-encoding': 'br, gzip, deflate'", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "simplify_api_base_url", "description": "简化API基础URL", "pattern": "https://d16\\.api\\.augmentcode\\.com/", "replacement": "API/", "enabled": true, "preserve_structure": false}, {"name": "shorten_request_id", "description": "缩短请求ID", "pattern": "'x-request-id': '([a-f0-9]{8})[a-f0-9-]{28}'", "replacement": "'req_id': '\\1'", "enabled": true, "preserve_structure": false}, {"name": "shorten_session_id", "description": "缩短会话ID", "pattern": "'x-request-session-id': '([a-f0-9]{8})[a-f0-9-]{28}'", "replacement": "'sess_id': '\\1'", "enabled": true, "preserve_structure": false}, {"name": "auth_token", "description": "缩短认证Token", "pattern": ",?\\s*'Authorization': 'Bearer ([a-f0-9]{8})[a-f0-9]{56}'", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "checkpoint_not_found", "description": "checkpoint_not_found", "pattern": ",?\\s*\"checkpoint_not_found\":\\s*false", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "shorten_blob_name", "description": "缩短blob名称", "pattern": "\"blob_name\":\"([a-f0-9]{8})[a-f0-9]{56}\"", "replacement": "\"blob\":\"\\1\"", "enabled": true, "preserve_structure": false}, {"name": "remove_milliseconds", "description": "移除时间戳毫秒部分", "pattern": "(\\d{2}:\\d{2}:\\d{2}),\\d{3}", "replacement": "\\1", "enabled": true, "preserve_structure": false}, {"name": "content_length", "description": "Content-Length", "pattern": ",?\\s*'Content-Length': '\\d+'", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "response_content_type", "description": "响应Content-Type", "pattern": ",?\\s*'content-type': 'application/json'", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "date_header", "description": "简化Date头", "pattern": ",?\\s*'date': '[^']*'", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "via_header", "description": "Via头", "pattern": ",?\\s*'Via': '1\\.1 google'", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "alt_svc", "description": "简化Alt-Svc头", "pattern": ",?\\s*'Alt-Svc': '[^']*'", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "api_version", "description": "移除API版本字段", "pattern": ",?\\s*'x-api-version': '[^']*'", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "transfer_encoding", "description": "移除Transfer-Encoding字段", "pattern": ",?\\s*'Transfer-Encoding': 'chunked'", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "remove_empty", "description": "移除null/[]/{}/''/\"\" key-value", "pattern": ",?\\s*\"\\w+\"\\s*:\\s*(null|\\[\\s*\\]|\\{\\s*\\}|\"\\s*\"|'\\s*')|\"unknown\"", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "remove_time", "description": "移除不关心的time", "pattern": ",?\\s*\"\\w*time\\w*\"\\s*:\\s*\\d+", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "subscription-info", "description": "订阅校验", "pattern": "\\d+:\\d+:\\d+ .+: POST API/subscription-info(\\n.+?)+?false\\}\\}\\}\n*", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "empty-header-response", "description": "空请求头/体", "pattern": "\\d+-\\d+-\\d+ \\d+:\\d+:\\d+ .+?: \\{\\}\\n", "replacement": "", "enabled": true, "preserve_structure": false}, {"name": "count_json_objects", "description": "统计JSON对象数量", "pattern": "\\{[^{}]*\\}", "replacement": "", "enabled": false, "preserve_structure": true}]}