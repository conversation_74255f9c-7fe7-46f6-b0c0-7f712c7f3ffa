# 配置要监控的目标URLs
target_urls:
  - "api.augmentcode.com/chat-stream"
  - "api.anthropic.com"
  - "api.openai.com"
  - "generativelanguage.googleapis.com"
  - "open.bigmodel.cn"
  - "dashscope.aliyuncs.com"
  - "api.moonshot.cn"
  - "api.github.com"
  # 添加更多目标URL

# 代理服务器配置
listen_port: 8080          # 本地监听端口
upstream_host: "127.0.0.1" # 上游代理主机
upstream_port: 10809       # 上游代理端口
upstream_mode: "http"      # 上游代理模式: "http" 或 "socks5"

# HTTPS拦截说明:
# 1. 程序已启用HTTPS拦截功能
# 2. 首次运行会在 ~/.mitmproxy/ 目录生成CA证书
# 3. 需要安装并信任该证书才能拦截HTTPS流量
# 4. 运行 python install_cert.py 自动安装证书
# 5. 或访问 http://mitm.it 手动下载安装证书