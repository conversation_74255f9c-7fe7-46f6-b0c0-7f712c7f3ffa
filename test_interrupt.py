#!/usr/bin/env python3
"""
测试程序中断处理的脚本
"""
import subprocess
import time
import signal
import os
import sys

def test_interrupt():
    """测试程序的中断处理"""
    print("🧪 测试程序中断处理...")

    # 启动主程序
    print("启动主程序...")
    process = subprocess.Popen(
        [sys.executable, "main.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True,
        bufsize=1,
        universal_newlines=True
    )

    try:
        # 等待程序启动
        print("等待程序启动...")
        time.sleep(3)

        # 检查程序是否正在运行
        if process.poll() is not None:
            print("❌ 程序启动失败")
            stdout, _ = process.communicate()
            print(f"输出: {stdout}")
            return False

        print("✅ 程序已启动")

        # 发送中断信号（相当于Ctrl+C）
        print("发送中断信号...")
        if os.name == 'nt':  # Windows
            # 在Windows上使用CTRL_C_EVENT
            try:
                process.send_signal(signal.CTRL_C_EVENT)
            except AttributeError:
                # 如果不支持CTRL_C_EVENT，使用terminate
                print("使用terminate方法...")
                process.terminate()
        else:
            # Unix/Linux系统使用SIGINT
            process.send_signal(signal.SIGINT)

        # 等待程序退出
        print("等待程序退出...")
        try:
            stdout, _ = process.communicate(timeout=10)
            print(f"程序退出码: {process.returncode}")
            print(f"程序输出:\n{stdout}")

            if process.returncode == 0:
                print("✅ 程序正常退出")
                return True
            else:
                print(f"⚠️  程序退出码非零: {process.returncode}")
                return True  # 对于信号中断，非零退出码是正常的

        except subprocess.TimeoutExpired:
            print("❌ 程序未在预期时间内退出，强制终止")
            process.kill()
            stdout, _ = process.communicate()
            print(f"强制终止后的输出:\n{stdout}")
            return False

    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        if process.poll() is None:
            process.kill()
        return False

def main():
    """主函数"""
    print("🚀 log-proxy程序中断测试")
    print("=" * 40)

    if test_interrupt():
        print("\n🎉 中断测试通过！")
    else:
        print("\n❌ 中断测试失败！")

if __name__ == "__main__":
    main()
